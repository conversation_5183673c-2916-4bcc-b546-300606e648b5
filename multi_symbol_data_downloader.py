"""
多幣種數據下載器
下載多個加密貨幣的15分鐘K線數據用於CVD策略測試

支持幣種: ETH, SOL, BNB, TRB, 1000PEPE, DOGE, XRP

作者: 專業量化策略工程師
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
from typing import List, Dict
import logging

class MultiSymbolDataDownloader:
    """多幣種數據下載器"""
    
    def __init__(self):
        self.base_url = "https://fapi.binance.com"
        self.symbols = [
            "ETHUSDT",
            "SOLUSDT", 
            "BNBUSDT",
            "TRBUSDT",
            "1000PEPEUSDT",
            "DOGEUSDT",
            "XRPUSDT"
        ]
        
        # 設置日誌
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 創建數據存儲目錄
        self.data_dir = "multi_symbol_data"
        os.makedirs(self.data_dir, exist_ok=True)
        
        self.session = None
    
    async def __aenter__(self):
        """異步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """異步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    def date_to_timestamp(self, date_str: str) -> int:
        """將日期字符串轉換為毫秒時間戳"""
        dt = pd.to_datetime(date_str)
        return int(dt.timestamp() * 1000)
    
    async def download_symbol_data(self, symbol: str, 
                                  start_date: str = "2023-12-31", 
                                  end_date: str = "2025-06-30") -> pd.DataFrame:
        """下載單個幣種的K線數據"""
        try:
            self.logger.info(f"🚀 開始下載 {symbol} 數據...")
            
            start_timestamp = self.date_to_timestamp(start_date)
            end_timestamp = self.date_to_timestamp(end_date)
            
            all_data = []
            current_start = start_timestamp
            
            # 分批獲取數據
            while current_start < end_timestamp:
                url = f"{self.base_url}/fapi/v1/klines"
                params = {
                    "symbol": symbol,
                    "interval": "15m",
                    "startTime": current_start,
                    "endTime": end_timestamp,
                    "limit": 1500
                }
                
                async with self.session.get(url, params=params, timeout=30) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if data:
                            all_data.extend(data)
                            
                            # 更新下一批的開始時間
                            last_timestamp = int(data[-1][0])
                            current_start = last_timestamp + 1
                            
                            self.logger.info(f"  📥 {symbol}: 獲取 {len(data)} 條記錄")
                        else:
                            break
                    else:
                        self.logger.error(f"❌ {symbol} API響應異常: {response.status}")
                        break
                
                # 避免請求過於頻繁
                await asyncio.sleep(0.1)
            
            if all_data:
                # 轉換為 DataFrame
                df = pd.DataFrame(all_data, columns=[
                    'timestamp', 'open', 'high', 'low', 'close', 'volume',
                    'close_time', 'quote_asset_volume', 'number_of_trades',
                    'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
                ])
                
                # 轉換數據類型
                numeric_columns = ['open', 'high', 'low', 'close', 'volume', 
                                 'quote_asset_volume', 'number_of_trades',
                                 'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume']
                
                for col in numeric_columns:
                    df[col] = pd.to_numeric(df[col])
                
                # 轉換時間戳
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                df.set_index('timestamp', inplace=True)
                df = df.sort_index()
                
                # 去重
                df = df[~df.index.duplicated(keep='first')]
                
                # 重命名主要列
                df = df.rename(columns={
                    'open': 'Open',
                    'high': 'High',
                    'low': 'Low',
                    'close': 'Close',
                    'volume': 'Volume'
                })
                
                # 計算技術指標
                df = self.calculate_technical_indicators(df)
                
                self.logger.info(f"✅ {symbol} 數據處理完成: {len(df)} 條記錄")
                self.logger.info(f"   時間範圍: {df.index.min()} 到 {df.index.max()}")
                
                return df
            
            return pd.DataFrame()
            
        except Exception as e:
            self.logger.error(f"❌ {symbol} 數據下載失敗: {e}")
            return pd.DataFrame()
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """計算技術指標"""
        try:
            # 計算收益率
            df['returns'] = df['Close'].pct_change()
            
            # 計算移動平均線
            df['MA_20'] = df['Close'].rolling(window=20).mean()
            df['MA_50'] = df['Close'].rolling(window=50).mean()
            
            # 計算波動率
            df['volatility'] = df['returns'].rolling(window=20).std()
            
            # 計算價格變化
            df['price_change'] = df['Close'].diff()
            df['price_change_pct'] = df['Close'].pct_change() * 100
            
            # 計算成交量變化
            df['volume_change'] = df['Volume'].diff()
            df['volume_change_pct'] = df['Volume'].pct_change() * 100
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 技術指標計算失敗: {e}")
            return df
    
    async def download_all_symbols(self) -> Dict[str, pd.DataFrame]:
        """下載所有幣種數據"""
        self.logger.info(f"🚀 開始下載 {len(self.symbols)} 個幣種的數據")
        
        all_symbol_data = {}
        
        for symbol in self.symbols:
            try:
                # 下載數據
                symbol_data = await self.download_symbol_data(symbol)
                
                if not symbol_data.empty:
                    all_symbol_data[symbol] = symbol_data
                    
                    # 保存到文件
                    filename = f"{self.data_dir}/{symbol}_15m_data.csv"
                    symbol_data.to_csv(filename)
                    self.logger.info(f"💾 {symbol} 數據已保存: {filename}")
                    
                    # 生成數據摘要
                    summary = {
                        "symbol": symbol,
                        "download_time": datetime.now().isoformat(),
                        "records_count": len(symbol_data),
                        "date_range": {
                            "start": symbol_data.index.min().isoformat(),
                            "end": symbol_data.index.max().isoformat()
                        },
                        "price_stats": {
                            "min_price": float(symbol_data['Low'].min()),
                            "max_price": float(symbol_data['High'].max()),
                            "avg_price": float(symbol_data['Close'].mean()),
                            "total_volume": float(symbol_data['Volume'].sum())
                        }
                    }
                    
                    # 保存摘要
                    import json
                    summary_file = f"{self.data_dir}/{symbol}_summary.json"
                    with open(summary_file, 'w', encoding='utf-8') as f:
                        json.dump(summary, f, ensure_ascii=False, indent=2)
                
                else:
                    self.logger.warning(f"⚠️ {symbol} 數據為空")
                
            except Exception as e:
                self.logger.error(f"❌ {symbol} 處理失敗: {e}")
            
            # 避免請求過於頻繁
            await asyncio.sleep(1)
        
        self.logger.info(f"✅ 數據下載完成，成功獲取 {len(all_symbol_data)}/{len(self.symbols)} 個幣種")
        
        return all_symbol_data
    
    def generate_download_report(self, all_data: Dict[str, pd.DataFrame]):
        """生成下載報告"""
        report = []
        report.append("=" * 80)
        report.append("📊 多幣種數據下載報告")
        report.append("=" * 80)
        report.append(f"下載時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"目標幣種: {len(self.symbols)} 個")
        report.append(f"成功下載: {len(all_data)} 個")
        report.append("")
        
        if all_data:
            report.append("📈 各幣種數據統計:")
            report.append(f"{'幣種':<12} {'記錄數':<8} {'開始時間':<20} {'結束時間':<20} {'平均價格':<12}")
            report.append("-" * 80)
            
            for symbol, data in all_data.items():
                avg_price = data['Close'].mean()
                report.append(f"{symbol:<12} {len(data):<8} {str(data.index.min())[:19]:<20} {str(data.index.max())[:19]:<20} ${avg_price:<11.2f}")
        
        report.append("")
        report.append("=" * 80)
        
        # 保存報告
        report_text = "\n".join(report)
        with open(f"{self.data_dir}/download_report.txt", "w", encoding="utf-8") as f:
            f.write(report_text)
        
        print(report_text)
        return report_text

async def main():
    """主函數"""
    print("🚀 多幣種數據下載器")
    print("=" * 60)
    
    async with MultiSymbolDataDownloader() as downloader:
        # 下載所有幣種數據
        all_data = await downloader.download_all_symbols()
        
        # 生成報告
        downloader.generate_download_report(all_data)
        
        print(f"\n🎉 數據下載完成!")
        print(f"📁 數據保存位置: {downloader.data_dir}/")

if __name__ == "__main__":
    asyncio.run(main())
