"""
TI 突破策略參數優化測試
測試不同的分位數和盈虧比組合

參數組合:
1. 70/30 + 1:1 盈虧比
2. 70/30 + 2:1 盈虧比  
3. 80/20 + 1:1 盈虧比
4. 80/20 + 2:1 盈虧比
5. 60/40 + 1:1 盈虧比
6. 60/40 + 2:1 盈虧比

作者: 專業量化策略工程師
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
import json
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class TIParameterOptimizer:
    """TI 策略參數優化器"""
    
    def __init__(self):
        self.timeframes = ['5min', '15min', '1h']
        
        # 參數組合定義
        self.parameter_sets = {
            'Set1_70_30_1_1': {'upper_pct': 0.70, 'lower_pct': 0.30, 'profit_ratio': 1.0, 'loss_ratio': 1.0},
            'Set2_70_30_2_1': {'upper_pct': 0.70, 'lower_pct': 0.30, 'profit_ratio': 2.0, 'loss_ratio': 1.0},
            'Set3_80_20_1_1': {'upper_pct': 0.80, 'lower_pct': 0.20, 'profit_ratio': 1.0, 'loss_ratio': 1.0},
            'Set4_80_20_2_1': {'upper_pct': 0.80, 'lower_pct': 0.20, 'profit_ratio': 2.0, 'loss_ratio': 1.0},
            'Set5_60_40_1_1': {'upper_pct': 0.60, 'lower_pct': 0.40, 'profit_ratio': 1.0, 'loss_ratio': 1.0},
            'Set6_60_40_2_1': {'upper_pct': 0.60, 'lower_pct': 0.40, 'profit_ratio': 2.0, 'loss_ratio': 1.0}
        }
        
        self.results = {}
        
        # 創建結果目錄
        self.results_dir = "ti_parameter_optimization"
        os.makedirs(self.results_dir, exist_ok=True)
    
    def load_data(self, timeframe: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """載入指定時框的 TI 和價格數據"""
        try:
            # 載入 TI 數據 - 修正文件路徑
            ti_file = f"multi_timeframe_ti_data/TI_BTCUSDT_{timeframe}.csv"
            ti_data = pd.read_csv(ti_file, index_col=0, parse_dates=True)

            # 載入價格數據
            price_file = f"multi_timeframe_price_data/Price_BTCUSDT_{timeframe}.csv"
            price_data = pd.read_csv(price_file, index_col=0, parse_dates=True)

            return ti_data, price_data

        except Exception as e:
            print(f"❌ {timeframe} 數據載入失敗: {e}")
            return None, None
    
    def calculate_custom_bollinger_bands(self, ti_data: pd.DataFrame, timeframe: str, 
                                       upper_pct: float, lower_pct: float) -> pd.DataFrame:
        """計算自定義分位數的 TI 布林帶"""
        try:
            # 時框窗口映射
            window_map = {'5min': 12, '15min': 16, '1h': 24}
            window = window_map[timeframe]
            
            df = ti_data.copy()
            
            # 計算滾動統計
            rolling_stats = df['taker_intensity'].rolling(window=window)
            
            # 計算自定義分位數軌道
            df['ti_middle'] = rolling_stats.mean()
            df['ti_upper'] = rolling_stats.quantile(upper_pct)
            df['ti_lower'] = rolling_stats.quantile(lower_pct)
            
            # 計算相對位置
            df['ti_position'] = (df['taker_intensity'] - df['ti_lower']) / (df['ti_upper'] - df['ti_lower'])
            
            # 計算突破信號
            df['upper_breakout'] = (df['taker_intensity'] > df['ti_upper']).astype(int)
            df['lower_breakout'] = (df['taker_intensity'] < df['ti_lower']).astype(int)
            
            # 移除初始的NaN值
            df = df.dropna()
            
            return df
            
        except Exception as e:
            print(f"❌ {timeframe} 自定義布林帶計算失敗: {e}")
            return ti_data
    
    def merge_data(self, ti_data: pd.DataFrame, price_data: pd.DataFrame) -> pd.DataFrame:
        """合併 TI 和價格數據"""
        try:
            merged = pd.merge_asof(
                price_data.sort_index(),
                ti_data.sort_index(),
                left_index=True,
                right_index=True,
                direction='backward'
            )
            
            merged = merged.dropna()
            return merged
            
        except Exception as e:
            print(f"❌ 數據合併失敗: {e}")
            return pd.DataFrame()
    
    def generate_signals(self, data: pd.DataFrame, profit_ratio: float, loss_ratio: float) -> pd.DataFrame:
        """生成交易信號"""
        try:
            df = data.copy()
            
            # 生成做多信號：TI 突破上軌
            df['long_signal'] = (
                (df['taker_intensity'] > df['ti_upper']) & 
                (df['taker_intensity'].shift(1) <= df['ti_upper'].shift(1))
            ).astype(int)
            
            # 計算止盈止損價格
            df['entry_price'] = df['Close']
            df['stop_loss'] = df['entry_price'] - (df['atr'] * loss_ratio)
            df['take_profit'] = df['entry_price'] + (df['atr'] * profit_ratio)
            
            # 計算風險回報比
            df['risk_reward_ratio'] = (df['take_profit'] - df['entry_price']) / (df['entry_price'] - df['stop_loss'])
            
            return df
            
        except Exception as e:
            print(f"❌ 信號生成失敗: {e}")
            return data
    
    def backtest_strategy(self, data: pd.DataFrame, timeframe: str, param_set: str) -> Dict:
        """執行策略回測"""
        try:
            trades = []
            current_position = None
            
            for i, (timestamp, row) in enumerate(data.iterrows()):
                # 檢查開倉信號
                if row['long_signal'] == 1 and current_position is None:
                    current_position = {
                        'entry_time': timestamp,
                        'entry_price': row['Close'],
                        'stop_loss': row['stop_loss'],
                        'take_profit': row['take_profit'],
                        'atr': row['atr'],
                        'ti_value': row['taker_intensity'],
                        'ti_upper': row['ti_upper']
                    }
                
                # 檢查平倉條件
                elif current_position is not None:
                    exit_reason = None
                    exit_price = None
                    
                    # 檢查止盈
                    if row['High'] >= current_position['take_profit']:
                        exit_reason = 'take_profit'
                        exit_price = current_position['take_profit']
                    
                    # 檢查止損
                    elif row['Low'] <= current_position['stop_loss']:
                        exit_reason = 'stop_loss'
                        exit_price = current_position['stop_loss']
                    
                    # 如果觸發平倉條件
                    if exit_reason:
                        pnl = exit_price - current_position['entry_price']
                        pnl_pct = (pnl / current_position['entry_price']) * 100
                        
                        trade_record = {
                            'entry_time': current_position['entry_time'],
                            'exit_time': timestamp,
                            'entry_price': current_position['entry_price'],
                            'exit_price': exit_price,
                            'stop_loss': current_position['stop_loss'],
                            'take_profit': current_position['take_profit'],
                            'pnl': pnl,
                            'pnl_pct': pnl_pct,
                            'exit_reason': exit_reason,
                            'duration_hours': (timestamp - current_position['entry_time']).total_seconds() / 3600,
                            'atr': current_position['atr'],
                            'ti_value': current_position['ti_value'],
                            'ti_upper': current_position['ti_upper']
                        }
                        
                        trades.append(trade_record)
                        current_position = None
            
            # 轉換為 DataFrame
            trades_df = pd.DataFrame(trades)
            
            if len(trades_df) == 0:
                return self._empty_results(timeframe, param_set)
            
            # 計算績效指標
            results = self._calculate_performance_metrics(trades_df, timeframe, param_set)
            
            return results
            
        except Exception as e:
            print(f"❌ {timeframe} {param_set} 回測失敗: {e}")
            return self._empty_results(timeframe, param_set)
    
    def _calculate_performance_metrics(self, trades_df: pd.DataFrame, timeframe: str, param_set: str) -> Dict:
        """計算績效指標"""
        try:
            total_trades = len(trades_df)
            winning_trades = len(trades_df[trades_df['pnl'] > 0])
            losing_trades = len(trades_df[trades_df['pnl'] < 0])
            
            # 基本指標
            win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
            
            # 盈虧統計
            total_pnl_pct = trades_df['pnl_pct'].sum()
            avg_win = trades_df[trades_df['pnl'] > 0]['pnl_pct'].mean() if winning_trades > 0 else 0
            avg_loss = trades_df[trades_df['pnl'] < 0]['pnl_pct'].mean() if losing_trades > 0 else 0
            
            # 盈利因子
            gross_profit = trades_df[trades_df['pnl'] > 0]['pnl_pct'].sum()
            gross_loss = abs(trades_df[trades_df['pnl'] < 0]['pnl_pct'].sum())
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
            
            # 最大回撤
            cumulative_returns = trades_df['pnl_pct'].cumsum()
            running_max = cumulative_returns.expanding().max()
            drawdown = cumulative_returns - running_max
            max_drawdown = drawdown.min()
            
            # Sharpe 比率
            returns_std = trades_df['pnl_pct'].std()
            sharpe_ratio = (trades_df['pnl_pct'].mean() / returns_std) if returns_std > 0 else 0
            
            # 平均持倉時間
            avg_duration = trades_df['duration_hours'].mean()
            
            results = {
                'timeframe': timeframe,
                'parameter_set': param_set,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'total_pnl_pct': total_pnl_pct,
                'avg_win_pct': avg_win,
                'avg_loss_pct': avg_loss,
                'profit_factor': profit_factor,
                'max_drawdown_pct': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'avg_duration_hours': avg_duration,
                'gross_profit_pct': gross_profit,
                'gross_loss_pct': gross_loss
            }
            
            return results
            
        except Exception as e:
            print(f"❌ 績效計算失敗: {e}")
            return self._empty_results(timeframe, param_set)
    
    def _empty_results(self, timeframe: str, param_set: str) -> Dict:
        """返回空結果"""
        return {
            'timeframe': timeframe,
            'parameter_set': param_set,
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'win_rate': 0,
            'total_pnl_pct': 0,
            'avg_win_pct': 0,
            'avg_loss_pct': 0,
            'profit_factor': 0,
            'max_drawdown_pct': 0,
            'sharpe_ratio': 0,
            'avg_duration_hours': 0,
            'gross_profit_pct': 0,
            'gross_loss_pct': 0
        }
    
    def run_parameter_optimization(self):
        """執行參數優化測試"""
        print("🚀 啟動 TI 策略參數優化測試")
        print("="*80)
        
        all_results = []
        
        for param_name, params in self.parameter_sets.items():
            print(f"\n📊 測試參數組合: {param_name}")
            print(f"   分位數: {params['upper_pct']*100:.0f}/{params['lower_pct']*100:.0f}")
            print(f"   盈虧比: {params['profit_ratio']:.1f}:1")
            
            for timeframe in self.timeframes:
                print(f"\n  🔍 處理 {timeframe} 時框...")
                
                # 載入數據
                ti_data, price_data = self.load_data(timeframe)
                
                if ti_data is None or price_data is None:
                    continue
                
                # 計算自定義布林帶
                custom_ti_data = self.calculate_custom_bollinger_bands(
                    ti_data, timeframe, params['upper_pct'], params['lower_pct']
                )
                
                # 合併數據
                merged_data = self.merge_data(custom_ti_data, price_data)
                
                if merged_data.empty:
                    continue
                
                # 生成信號
                signal_data = self.generate_signals(
                    merged_data, params['profit_ratio'], params['loss_ratio']
                )
                
                # 執行回測
                results = self.backtest_strategy(signal_data, timeframe, param_name)
                
                if results['total_trades'] > 0:
                    all_results.append(results)
                    
                    print(f"    ✅ {timeframe}: {results['total_trades']}筆交易, "
                          f"勝率{results['win_rate']:.1f}%, "
                          f"收益{results['total_pnl_pct']:.1f}%, "
                          f"Sharpe{results['sharpe_ratio']:.3f}")
        
        # 生成優化報告
        if all_results:
            self.generate_optimization_report(all_results)
        
        print("\n🎉 參數優化測試完成！")
        return all_results

    def generate_optimization_report(self, all_results: List[Dict]):
        """生成參數優化報告"""
        try:
            print("\n📋 生成參數優化報告...")

            # 創建結果 DataFrame
            results_df = pd.DataFrame(all_results)

            # 保存詳細結果
            results_file = f"{self.results_dir}/parameter_optimization_results.csv"
            results_df.to_csv(results_file, index=False)

            # 生成文字報告
            report = []
            report.append("="*100)
            report.append("📊 TI 突破策略 - 參數優化測試報告")
            report.append("="*100)
            report.append(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report.append("")

            # 參數組合說明
            report.append("🔧 測試參數組合:")
            for param_name, params in self.parameter_sets.items():
                report.append(f"  {param_name}: {params['upper_pct']*100:.0f}/{params['lower_pct']*100:.0f}分位數, {params['profit_ratio']:.1f}:1盈虧比")
            report.append("")

            # 按時框分組分析
            for timeframe in self.timeframes:
                tf_results = results_df[results_df['timeframe'] == timeframe]
                if len(tf_results) == 0:
                    continue

                report.append(f"📈 {timeframe} 時框結果:")
                report.append("")

                # 按 Sharpe 比率排序
                tf_sorted = tf_results.sort_values('sharpe_ratio', ascending=False)

                for _, row in tf_sorted.iterrows():
                    params = self.parameter_sets[row['parameter_set']]
                    report.append(f"  🔸 {row['parameter_set']}:")
                    report.append(f"     分位數: {params['upper_pct']*100:.0f}/{params['lower_pct']*100:.0f}, 盈虧比: {params['profit_ratio']:.1f}:1")
                    report.append(f"     交易數: {row['total_trades']}, 勝率: {row['win_rate']:.2f}%")
                    report.append(f"     總收益: {row['total_pnl_pct']:.2f}%, 盈利因子: {row['profit_factor']:.2f}")
                    report.append(f"     最大回撤: {row['max_drawdown_pct']:.2f}%, Sharpe: {row['sharpe_ratio']:.3f}")
                    report.append("")

                # 找出該時框最佳參數
                best_sharpe = tf_sorted.iloc[0]
                best_return = tf_results.loc[tf_results['total_pnl_pct'].idxmax()]

                report.append(f"  🏆 {timeframe} 最佳 Sharpe: {best_sharpe['parameter_set']} ({best_sharpe['sharpe_ratio']:.3f})")
                report.append(f"  💰 {timeframe} 最高收益: {best_return['parameter_set']} ({best_return['total_pnl_pct']:.2f}%)")
                report.append("")

            # 全局最佳參數
            report.append("🎯 全局最佳參數:")
            best_overall_sharpe = results_df.loc[results_df['sharpe_ratio'].idxmax()]
            best_overall_return = results_df.loc[results_df['total_pnl_pct'].idxmax()]

            report.append(f"  🏆 最佳 Sharpe 比率: {best_overall_sharpe['parameter_set']} - {best_overall_sharpe['timeframe']}")
            report.append(f"     Sharpe: {best_overall_sharpe['sharpe_ratio']:.3f}, 收益: {best_overall_sharpe['total_pnl_pct']:.2f}%")
            report.append(f"  💰 最高總收益: {best_overall_return['parameter_set']} - {best_overall_return['timeframe']}")
            report.append(f"     收益: {best_overall_return['total_pnl_pct']:.2f}%, Sharpe: {best_overall_return['sharpe_ratio']:.3f}")
            report.append("")

            # 參數影響分析
            report.append("📊 參數影響分析:")

            # 分位數影響
            pct_70_30 = results_df[results_df['parameter_set'].str.contains('70_30')]['sharpe_ratio'].mean()
            pct_80_20 = results_df[results_df['parameter_set'].str.contains('80_20')]['sharpe_ratio'].mean()
            pct_60_40 = results_df[results_df['parameter_set'].str.contains('60_40')]['sharpe_ratio'].mean()

            report.append(f"  分位數設置影響 (平均Sharpe):")
            report.append(f"    70/30: {pct_70_30:.3f}")
            report.append(f"    80/20: {pct_80_20:.3f}")
            report.append(f"    60/40: {pct_60_40:.3f}")

            # 盈虧比影響
            ratio_1_1 = results_df[results_df['parameter_set'].str.contains('1_1')]['sharpe_ratio'].mean()
            ratio_2_1 = results_df[results_df['parameter_set'].str.contains('2_1')]['sharpe_ratio'].mean()

            report.append(f"  盈虧比設置影響 (平均Sharpe):")
            report.append(f"    1:1 盈虧比: {ratio_1_1:.3f}")
            report.append(f"    2:1 盈虧比: {ratio_2_1:.3f}")
            report.append("")

            # 建議
            report.append("💡 優化建議:")
            if best_overall_sharpe['sharpe_ratio'] > 0.1:
                report.append("   ✅ 找到了較好的參數組合，建議進一步測試")
            elif best_overall_sharpe['sharpe_ratio'] > 0.05:
                report.append("   ⚠️ 參數有改善，但仍需進一步優化")
            else:
                report.append("   ❌ 所有參數組合表現不佳，建議重新考慮策略邏輯")

            report.append("")
            report.append("="*100)

            # 保存報告
            report_text = "\n".join(report)
            report_file = f"{self.results_dir}/optimization_report.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_text)

            print(f"✅ 優化報告已保存: {report_file}")
            print("\n" + report_text)

            # 生成對比圖表
            self.create_optimization_charts(results_df)

        except Exception as e:
            print(f"❌ 報告生成失敗: {e}")

    def create_optimization_charts(self, results_df: pd.DataFrame):
        """創建參數優化對比圖表"""
        try:
            print("📊 生成參數優化圖表...")

            # 設置圖表樣式
            plt.style.use('default')
            fig, axes = plt.subplots(2, 3, figsize=(18, 12))
            fig.suptitle('TI Strategy Parameter Optimization Results', fontsize=16, fontweight='bold')

            # 為每個時框創建子圖
            timeframes = ['5min', '15min', '1h']

            for i, timeframe in enumerate(timeframes):
                tf_data = results_df[results_df['timeframe'] == timeframe]
                if len(tf_data) == 0:
                    continue

                # Sharpe 比率對比
                axes[0, i].bar(range(len(tf_data)), tf_data['sharpe_ratio'],
                              color='skyblue', alpha=0.7)
                axes[0, i].set_title(f'{timeframe} - Sharpe Ratio')
                axes[0, i].set_ylabel('Sharpe Ratio')
                axes[0, i].set_xticks(range(len(tf_data)))
                axes[0, i].set_xticklabels([p.replace('Set', '').replace('_', '\n')
                                          for p in tf_data['parameter_set']], rotation=45, fontsize=8)

                # 總收益對比
                colors = ['green' if x > 0 else 'red' for x in tf_data['total_pnl_pct']]
                axes[1, i].bar(range(len(tf_data)), tf_data['total_pnl_pct'],
                              color=colors, alpha=0.7)
                axes[1, i].set_title(f'{timeframe} - Total Return')
                axes[1, i].set_ylabel('Total Return (%)')
                axes[1, i].set_xticks(range(len(tf_data)))
                axes[1, i].set_xticklabels([p.replace('Set', '').replace('_', '\n')
                                          for p in tf_data['parameter_set']], rotation=45, fontsize=8)
                axes[1, i].axhline(y=0, color='black', linestyle='-', alpha=0.3)

            plt.tight_layout()
            chart_file = f"{self.results_dir}/optimization_charts.png"
            plt.savefig(chart_file, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"✅ 優化圖表已保存: {chart_file}")

        except Exception as e:
            print(f"❌ 圖表生成失敗: {e}")

def main():
    """主函數"""
    optimizer = TIParameterOptimizer()
    results = optimizer.run_parameter_optimization()

    if results:
        print(f"\n✅ 成功完成 {len(results)} 個參數組合的測試")
        print("📁 生成的文件:")
        print("  - ti_parameter_optimization/parameter_optimization_results.csv")
        print("  - ti_parameter_optimization/optimization_report.txt")
        print("  - ti_parameter_optimization/optimization_charts.png")
    else:
        print("\n❌ 優化測試失敗，請檢查數據文件")

if __name__ == "__main__":
    main()
