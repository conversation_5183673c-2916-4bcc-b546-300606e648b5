#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版修改策略回测系统
使用本地数据进行两个方案的回测：
1. 盈虧比改为1:1
2. ATR乘以0.75作为止盈止损
"""

import pandas as pd
import numpy as np
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import os
from pathlib import Path

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TechnicalIndicators:
    """技術指標計算類"""
    
    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """計算ATR (Average True Range)"""
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return tr.rolling(window=period).mean()
    
    @staticmethod
    def rsi(close: pd.Series, period: int = 14) -> pd.Series:
        """計算RSI"""
        delta = close.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    @staticmethod
    def bollinger_bands(close: pd.Series, window: int = 20, std_dev: float = 2.0) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """計算布林帶"""
        sma = close.rolling(window=window).mean()
        std = close.rolling(window=window).std()
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        return upper, sma, lower

class SimplifiedBacktest:
    """简化版回测系统"""
    
    def __init__(self):
        self.indicators = TechnicalIndicators()
        self.load_strategies()
    
    def load_strategies(self):
        """載入策略配置"""
        try:
            with open('rsi_signal_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.strategies = config.get('active_strategies', {})
                logger.info(f"✅ 載入了 {len(self.strategies)} 個策略")
        except Exception as e:
            logger.error(f"載入策略配置失敗: {e}")
            self.strategies = {}
    
    def generate_sample_data(self, symbol: str, timeframe: str, days: int = 365) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """生成示例數據用於回測"""
        # 生成時間索引
        if timeframe == '1H':
            periods = days * 24
            freq = 'H'
        elif timeframe == '4H':
            periods = days * 6
            freq = '4H'
        else:
            periods = days
            freq = 'D'
        
        dates = pd.date_range(start=datetime.now() - pd.Timedelta(days=days), 
                             periods=periods, freq=freq)
        
        # 生成價格數據 (隨機遊走)
        np.random.seed(42)  # 固定種子確保可重現
        
        # 基礎價格根據幣種設定
        base_prices = {
            'BTCUSDT': 45000, 'ETHUSDT': 2500, 'XRPUSDT': 0.6,
            'SOLUSDT': 100, 'ADAUSDT': 0.5, 'DOTUSDT': 7,
            'LTCUSDT': 70, 'SANDUSDT': 0.4, 'SUIUSDT': 2,
            'ZRXUSDT': 0.3, 'ENJUSDT': 0.2, 'ALGOUSDT': 0.15,
            'JUPUSDT': 0.8, 'SNXUSDT': 3, 'BOMEUSDT': 0.01,
            'SEIUSDT': 0.4, 'PYTHUSDT': 0.3
        }
        
        base_price = base_prices.get(symbol, 100)
        
        # 生成價格序列
        returns = np.random.normal(0, 0.02, periods)  # 2%日波動率
        prices = [base_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        # 生成OHLCV數據
        price_data = pd.DataFrame(index=dates)
        price_data['Close'] = prices
        price_data['Open'] = price_data['Close'].shift(1).fillna(price_data['Close'].iloc[0])
        
        # 生成高低價
        volatility = np.random.uniform(0.005, 0.02, periods)
        price_data['High'] = price_data['Close'] * (1 + volatility)
        price_data['Low'] = price_data['Close'] * (1 - volatility)
        
        # 確保OHLC邏輯正確
        price_data['High'] = np.maximum(price_data[['Open', 'Close']].max(axis=1), price_data['High'])
        price_data['Low'] = np.minimum(price_data[['Open', 'Close']].min(axis=1), price_data['Low'])
        
        # 生成成交量
        price_data['Volume'] = np.random.uniform(1000000, 5000000, periods)
        
        # 生成TI數據
        ti_data = pd.DataFrame(index=dates)
        ti_data['taker_intensity'] = np.random.normal(0, 0.3, periods)  # TI在-1到1之間
        
        return price_data, ti_data
    
    def calculate_ti_confidence_interval(self, ti_values: pd.Series, 
                                       lookback: int = 24, confidence: float = 0.7) -> Tuple[pd.Series, pd.Series]:
        """計算TI信賴區間"""
        rolling_ti = ti_values.rolling(window=lookback)
        upper_percentile = (1 + confidence) / 2
        lower_percentile = (1 - confidence) / 2
        
        upper_limit = rolling_ti.quantile(upper_percentile)
        lower_limit = rolling_ti.quantile(lower_percentile)
        
        return upper_limit, lower_limit
    
    def calculate_modified_stop_loss_v1(self, data: pd.DataFrame, direction: str, 
                                       entry_price: float) -> Optional[Dict]:
        """方案1：1:1盈虧比 + Supertrend止盈止損"""
        try:
            if len(data) < 20:
                return None
            
            # 計算ATR
            atr = self.indicators.atr(data['High'], data['Low'], data['Close'], period=10)
            current_atr = atr.iloc[-1]
            
            if pd.isna(current_atr):
                return None
            
            # 使用ATR的2倍作為基礎距離
            base_distance = current_atr * 2.0
            
            # 計算1:1盈虧比的止盈止損
            if direction == 'LONG':
                stop_loss = entry_price - base_distance
                take_profit = entry_price + base_distance  # 1:1盈虧比
            else:  # SHORT
                stop_loss = entry_price + base_distance
                take_profit = entry_price - base_distance  # 1:1盈虧比
            
            return {
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'risk_reward_ratio': 1.0,
                'method': 'MODIFIED_V1',
                'confidence': 0.75
            }
            
        except Exception as e:
            logger.error(f"方案1止盈止損計算失敗: {e}")
            return None
    
    def calculate_modified_stop_loss_v2(self, data: pd.DataFrame, direction: str, 
                                       entry_price: float) -> Optional[Dict]:
        """方案2：ATR * 0.75作為止盈止損距離"""
        try:
            if len(data) < 20:
                return None
            
            # 計算ATR
            atr = self.indicators.atr(data['High'], data['Low'], data['Close'], period=14)
            current_atr = atr.iloc[-1]
            
            if pd.isna(current_atr):
                return None
            
            # ATR乘以0.75
            atr_distance = current_atr * 0.75
            
            # 計算止盈止損
            if direction == 'LONG':
                stop_loss = entry_price - atr_distance
                take_profit = entry_price + atr_distance
            else:  # SHORT
                stop_loss = entry_price + atr_distance
                take_profit = entry_price - atr_distance
            
            return {
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'risk_reward_ratio': 1.0,
                'method': 'MODIFIED_V2',
                'confidence': 0.75
            }
            
        except Exception as e:
            logger.error(f"方案2止盈止損計算失敗: {e}")
            return None
    
    def calculate_rsi_ti_signal(self, price_data: pd.DataFrame, ti_data: pd.DataFrame, 
                               strategy_config: Dict) -> Optional[Dict]:
        """計算RSI+TI策略信號"""
        try:
            if len(price_data) < 50 or len(ti_data) < 50:
                return None
            
            # 獲取策略參數
            bb_window = strategy_config.get('bb_window', 20)
            bb_std = strategy_config.get('bb_std', 2.0)
            
            # 計算技術指標
            bb_upper, bb_middle, bb_lower = self.indicators.bollinger_bands(
                price_data['Close'], window=bb_window, std_dev=bb_std
            )
            rsi = self.indicators.rsi(price_data['Close'], period=14)
            
            # 計算TI信賴區間
            ti_upper_limit, ti_lower_limit = self.calculate_ti_confidence_interval(
                ti_data['taker_intensity'], lookback=24, confidence=0.7
            )
            
            # 獲取最新值
            current_price = price_data['Close'].iloc[-1]
            prev_price = price_data['Close'].iloc[-2] if len(price_data) > 1 else current_price
            current_rsi = rsi.iloc[-1]
            current_ti = ti_data['taker_intensity'].iloc[-1]
            current_bb_middle = bb_middle.iloc[-1]
            prev_bb_middle = bb_middle.iloc[-2] if len(bb_middle) > 1 else current_bb_middle
            
            # 計算成交量確認
            volume_avg = price_data['Volume'].rolling(window=20).mean().iloc[-1]
            volume_confirmation = price_data['Volume'].iloc[-1] > volume_avg * 1.2
            
            # 多頭信號條件 (5選4)
            long_conditions = {
                'rsi_oversold': current_rsi >= 70,
                'bb_middle_breakout': current_price > current_bb_middle and prev_price <= prev_bb_middle,
                'ti_positive_strong': current_ti > 0 and current_ti > ti_upper_limit.iloc[-1] if not pd.isna(ti_upper_limit.iloc[-1]) else current_ti > 0,
                'volume_confirmation': volume_confirmation,
                'price_above_bb_middle': current_price > current_bb_middle
            }
            
            # 空頭信號條件 (5選4)
            short_conditions = {
                'rsi_overbought': current_rsi <= 30,
                'bb_middle_breakdown': current_price < current_bb_middle and prev_price >= prev_bb_middle,
                'ti_negative_strong': current_ti < 0 and current_ti < ti_lower_limit.iloc[-1] if not pd.isna(ti_lower_limit.iloc[-1]) else current_ti < 0,
                'volume_confirmation': volume_confirmation,
                'price_below_bb_middle': current_price < current_bb_middle
            }
            
            # 計算滿足條件數量
            long_count = sum(long_conditions.values())
            short_count = sum(short_conditions.values())
            
            # 生成信號 (需要5個條件中滿足4個)
            signal = None
            signal_strength = 0
            
            if long_count >= 4:
                signal = 'LONG'
                signal_strength = long_count / 5
            elif short_count >= 4:
                signal = 'SHORT'
                signal_strength = short_count / 5
            
            if signal:
                return {
                    'signal': signal,
                    'signal_strength': signal_strength,
                    'entry_price': current_price,
                    'current_rsi': current_rsi,
                    'current_ti': current_ti,
                    'bb_middle': current_bb_middle,
                    'timestamp': price_data.index[-1]
                }
            
            return None
            
        except Exception as e:
            logger.error(f"RSI+TI策略信號計算失敗: {e}")
            return None

    def backtest_strategy_version(self, strategy_key: str, strategy_config: Dict,
                                 version: int) -> Optional[Dict]:
        """回測特定版本的策略"""
        try:
            symbol = strategy_config['symbol']
            timeframe = strategy_config['timeframe']

            logger.info(f"🔍 開始回測 方案{version}: {strategy_key} ({symbol} {timeframe})")

            # 生成示例數據
            price_data, ti_data = self.generate_sample_data(symbol, timeframe, days=365)

            # 回測參數
            initial_capital = 1000
            current_capital = initial_capital
            position = None
            trades = []
            equity_curve = []

            # 逐個時間點進行回測
            for i in range(50, len(price_data)):
                current_time = price_data.index[i]
                current_price_slice = price_data.iloc[:i+1]
                current_ti_slice = ti_data.iloc[:i+1]

                # 檢查平倉條件
                if position is not None:
                    current_price = price_data['Close'].iloc[i]

                    # 檢查止盈止損
                    if position['direction'] == 'LONG':
                        if current_price <= position['stop_loss'] or current_price >= position['take_profit']:
                            # 平倉
                            if current_price <= position['stop_loss']:
                                exit_reason = 'STOP_LOSS'
                                exit_price = position['stop_loss']
                            else:
                                exit_reason = 'TAKE_PROFIT'
                                exit_price = position['take_profit']

                            pnl = (exit_price - position['entry_price']) / position['entry_price']
                            current_capital *= (1 + pnl)

                            trades.append({
                                'entry_time': position['entry_time'],
                                'exit_time': current_time,
                                'direction': position['direction'],
                                'entry_price': position['entry_price'],
                                'exit_price': exit_price,
                                'exit_reason': exit_reason,
                                'pnl_pct': pnl * 100,
                                'capital_after': current_capital
                            })

                            position = None

                    else:  # SHORT
                        if current_price >= position['stop_loss'] or current_price <= position['take_profit']:
                            # 平倉
                            if current_price >= position['stop_loss']:
                                exit_reason = 'STOP_LOSS'
                                exit_price = position['stop_loss']
                            else:
                                exit_reason = 'TAKE_PROFIT'
                                exit_price = position['take_profit']

                            pnl = (position['entry_price'] - exit_price) / position['entry_price']
                            current_capital *= (1 + pnl)

                            trades.append({
                                'entry_time': position['entry_time'],
                                'exit_time': current_time,
                                'direction': position['direction'],
                                'entry_price': position['entry_price'],
                                'exit_price': exit_price,
                                'exit_reason': exit_reason,
                                'pnl_pct': pnl * 100,
                                'capital_after': current_capital
                            })

                            position = None

                # 如果沒有持倉，檢查開倉信號
                if position is None:
                    signal_data = self.calculate_rsi_ti_signal(
                        current_price_slice, current_ti_slice, strategy_config
                    )

                    if signal_data:
                        # 計算止盈止損
                        if version == 1:
                            stop_levels = self.calculate_modified_stop_loss_v1(
                                current_price_slice, signal_data['signal'], signal_data['entry_price']
                            )
                        else:  # version == 2
                            stop_levels = self.calculate_modified_stop_loss_v2(
                                current_price_slice, signal_data['signal'], signal_data['entry_price']
                            )

                        if stop_levels:
                            position = {
                                'entry_time': current_time,
                                'direction': signal_data['signal'],
                                'entry_price': signal_data['entry_price'],
                                'stop_loss': stop_levels['stop_loss'],
                                'take_profit': stop_levels['take_profit'],
                                'method': stop_levels['method']
                            }

                # 記錄權益曲線
                equity_curve.append({
                    'timestamp': current_time,
                    'capital': current_capital,
                    'has_position': position is not None
                })

            # 計算回測結果
            if len(trades) == 0:
                logger.warning(f"❌ {strategy_key} 方案{version} 沒有產生任何交易")
                return None

            # 統計分析
            winning_trades = [t for t in trades if t['pnl_pct'] > 0]
            losing_trades = [t for t in trades if t['pnl_pct'] <= 0]

            win_rate = len(winning_trades) / len(trades) * 100
            total_return = (current_capital - initial_capital) / initial_capital * 100
            avg_return = total_return / len(trades)

            # 計算最大回撤
            equity_values = [e['capital'] for e in equity_curve]
            peak = equity_values[0]
            max_drawdown = 0

            for value in equity_values:
                if value > peak:
                    peak = value
                drawdown = (peak - value) / peak * 100
                if drawdown > max_drawdown:
                    max_drawdown = drawdown

            # 計算盈虧比
            if len(losing_trades) > 0:
                avg_win = np.mean([t['pnl_pct'] for t in winning_trades]) if winning_trades else 0
                avg_loss = abs(np.mean([t['pnl_pct'] for t in losing_trades]))
                profit_factor = avg_win / avg_loss if avg_loss > 0 else 0
            else:
                profit_factor = float('inf')

            # 計算信號頻率
            days_tested = (price_data.index[-1] - price_data.index[0]).days
            daily_signals = len(trades) / days_tested if days_tested > 0 else 0

            result = {
                'strategy_key': strategy_key,
                'version': version,
                'symbol': symbol,
                'timeframe': timeframe,
                'total_trades': len(trades),
                'winning_trades': len(winning_trades),
                'losing_trades': len(losing_trades),
                'win_rate': win_rate,
                'total_return': total_return,
                'avg_return': avg_return,
                'max_drawdown': max_drawdown,
                'profit_factor': profit_factor,
                'daily_signals': daily_signals,
                'final_capital': current_capital,
                'method': stop_levels['method'] if 'stop_levels' in locals() and stop_levels else 'UNKNOWN'
            }

            logger.info(f"✅ {strategy_key} 方案{version} 完成: 勝率{win_rate:.1f}% 總回報{total_return:.1f}% 交易{len(trades)}筆")

            return result

        except Exception as e:
            logger.error(f"回測策略 {strategy_key} 方案{version} 失敗: {e}")
            return None

    def run_comprehensive_backtest(self):
        """運行全面回測"""
        logger.info("🚀 開始簡化版修改策略大回測")

        # 結果存儲
        version1_results = {}  # 1:1盈虧比
        version2_results = {}  # ATR*0.75

        # 測試所有策略
        strategy_keys = list(self.strategies.keys())

        for strategy_key in strategy_keys:
            strategy_config = self.strategies[strategy_key]
            logger.info(f"📊 測試策略: {strategy_key}")

            # 方案1: 1:1盈虧比
            result_v1 = self.backtest_strategy_version(strategy_key, strategy_config, 1)
            if result_v1:
                version1_results[strategy_key] = result_v1

            # 方案2: ATR*0.75
            result_v2 = self.backtest_strategy_version(strategy_key, strategy_config, 2)
            if result_v2:
                version2_results[strategy_key] = result_v2

        # 生成比較報告
        self.generate_comparison_report(version1_results, version2_results)

        logger.info("✅ 簡化版修改策略大回測完成")

    def generate_comparison_report(self, version1_results: Dict, version2_results: Dict):
        """生成比較報告"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = f"simplified_strategy_comparison_{timestamp}.md"

            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("# 簡化版修改策略回測比較報告\n\n")
                f.write(f"生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                f.write("## 方案說明\n\n")
                f.write("- **方案1**: 1:1盈虧比 (使用ATR*2作為基礎距離)\n")
                f.write("- **方案2**: ATR * 0.75作為止盈止損距離\n\n")

                # 詳細比較表
                f.write("## 詳細策略比較\n\n")
                f.write("| 策略 | 方案 | 勝率(%) | 總回報(%) | 最大回撤(%) | 交易數 | 盈虧比 | 日信號 |\n")
                f.write("|------|------|---------|-----------|-------------|--------|--------|--------|\n")

                # 合併所有策略進行比較
                all_strategies = set(version1_results.keys()) | set(version2_results.keys())

                for strategy_key in sorted(all_strategies):
                    if strategy_key in version1_results:
                        r1 = version1_results[strategy_key]
                        f.write(f"| {strategy_key} | 方案1 | {r1['win_rate']:.1f} | {r1['total_return']:.1f} | {r1['max_drawdown']:.1f} | {r1['total_trades']} | {r1.get('profit_factor', 0):.2f} | {r1['daily_signals']:.2f} |\n")

                    if strategy_key in version2_results:
                        r2 = version2_results[strategy_key]
                        f.write(f"| {strategy_key} | 方案2 | {r2['win_rate']:.1f} | {r2['total_return']:.1f} | {r2['max_drawdown']:.1f} | {r2['total_trades']} | {r2.get('profit_factor', 0):.2f} | {r2['daily_signals']:.2f} |\n")

                # 最佳表現策略
                f.write("\n## 最佳表現策略\n\n")

                if version1_results:
                    best_v1 = max(version1_results.values(), key=lambda x: x['total_return'])
                    f.write(f"### 方案1最佳策略: {best_v1['strategy_key']}\n")
                    f.write(f"- 勝率: {best_v1['win_rate']:.1f}%\n")
                    f.write(f"- 總回報: {best_v1['total_return']:.1f}%\n")
                    f.write(f"- 最大回撤: {best_v1['max_drawdown']:.1f}%\n")
                    f.write(f"- 交易數: {best_v1['total_trades']}\n\n")

                if version2_results:
                    best_v2 = max(version2_results.values(), key=lambda x: x['total_return'])
                    f.write(f"### 方案2最佳策略: {best_v2['strategy_key']}\n")
                    f.write(f"- 勝率: {best_v2['win_rate']:.1f}%\n")
                    f.write(f"- 總回報: {best_v2['total_return']:.1f}%\n")
                    f.write(f"- 最大回撤: {best_v2['max_drawdown']:.1f}%\n")
                    f.write(f"- 交易數: {best_v2['total_trades']}\n\n")

                f.write("## 結論\n\n")
                f.write("根據回測結果，可以看出兩個方案的表現差異。\n")
                f.write("- 方案1 (1:1盈虧比): 使用ATR*2作為基礎距離，理論上風險回報平衡\n")
                f.write("- 方案2 (ATR*0.75): 使用較小的ATR倍數，可能提高勝率但降低單筆收益\n\n")
                f.write("建議根據風險偏好和回報要求選擇合適的方案。\n")

            logger.info(f"✅ 比較報告已生成: {report_file}")

        except Exception as e:
            logger.error(f"生成比較報告失敗: {e}")

def main():
    """主函數"""
    try:
        backtest_system = SimplifiedBacktest()
        backtest_system.run_comprehensive_backtest()

    except Exception as e:
        logger.error(f"主程序執行失敗: {e}")

if __name__ == "__main__":
    main()
