"""
Cumulative Volume Delta Divergence 指標 Python 實現
基於 TradingView Pine Script 轉換

功能:
1. 計算累積成交量差值 (CVD)
2. 檢測價格與 CVD 的背離
3. 生成 Bull/Bear 交易信號
4. 支援多時框分析

作者: 專業量化策略工程師
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Tuple, List, Optional
import warnings
warnings.filterwarnings('ignore')

class CVDDivergenceIndicator:
    """CVD 背離指標"""
    
    def __init__(self, 
                 lbL: int = 5,           # 左側回看期數
                 lbR: int = 5,           # 右側回看期數
                 rangeUpper: int = 60,   # 背離檢測最大範圍
                 rangeLower: int = 5):   # 背離檢測最小範圍
        
        self.lbL = lbL
        self.lbR = lbR
        self.rangeUpper = rangeUpper
        self.rangeLower = rangeLower
        
    def calculate_volume_delta(self, df: pd.DataFrame) -> pd.Series:
        """計算成交量差值"""
        volume_delta = []
        
        for i in range(len(df)):
            # 判斷買賣方向
            is_buy_volume = True
            
            if df['Close'].iloc[i] > df['Open'].iloc[i]:
                is_buy_volume = True
            elif df['Close'].iloc[i] < df['Open'].iloc[i]:
                is_buy_volume = False
            elif i > 0:
                if df['Close'].iloc[i] > df['Close'].iloc[i-1]:
                    is_buy_volume = True
                elif df['Close'].iloc[i] < df['Close'].iloc[i-1]:
                    is_buy_volume = False
            
            # 計算成交量差值
            if is_buy_volume:
                delta = df['Volume'].iloc[i]
            else:
                delta = -df['Volume'].iloc[i]
            
            volume_delta.append(delta)
        
        return pd.Series(volume_delta, index=df.index)
    
    def calculate_cumulative_volume_delta(self, df: pd.DataFrame) -> pd.Series:
        """計算累積成交量差值 (CVD)"""
        volume_delta = self.calculate_volume_delta(df)
        
        # 計算累積 CVD
        cvd = volume_delta.cumsum()
        
        return cvd
    
    def find_pivot_points(self, series: pd.Series, left: int, right: int) -> Tuple[pd.Series, pd.Series]:
        """尋找樞軸點 (高點和低點)"""
        pivot_highs = pd.Series(index=series.index, dtype=float)
        pivot_lows = pd.Series(index=series.index, dtype=float)
        
        for i in range(left, len(series) - right):
            # 檢查是否為高點
            is_high = True
            for j in range(i - left, i + right + 1):
                if j != i and series.iloc[j] >= series.iloc[i]:
                    is_high = False
                    break
            
            if is_high:
                pivot_highs.iloc[i] = series.iloc[i]
            
            # 檢查是否為低點
            is_low = True
            for j in range(i - left, i + right + 1):
                if j != i and series.iloc[j] <= series.iloc[i]:
                    is_low = False
                    break
            
            if is_low:
                pivot_lows.iloc[i] = series.iloc[i]
        
        return pivot_highs, pivot_lows
    
    def check_in_range(self, condition_series: pd.Series, current_idx: int) -> bool:
        """檢查條件是否在指定範圍內"""
        # 找到最近一次條件為 True 的位置
        recent_true_indices = condition_series[:current_idx][::-1]
        
        for i, (idx, value) in enumerate(recent_true_indices.items()):
            if value:
                bars_since = current_idx - idx
                return self.rangeLower <= bars_since <= self.rangeUpper
        
        return False
    
    def detect_divergences(self, df: pd.DataFrame) -> Tuple[pd.Series, pd.Series]:
        """檢測背離信號"""
        # 計算 CVD
        cvd = self.calculate_cumulative_volume_delta(df)
        
        # 找到樞軸點
        cvd_highs, cvd_lows = self.find_pivot_points(cvd, self.lbL, self.lbR)
        price_highs = df['High']
        price_lows = df['Low']
        
        # 初始化信號序列
        bull_signals = pd.Series(False, index=df.index)
        bear_signals = pd.Series(False, index=df.index)
        
        # 檢測牛市背離 (價格更低低點，CVD 更高低點)
        for i in range(self.lbL + self.lbR, len(df)):
            current_idx = i - self.lbR
            
            if pd.notna(cvd_lows.iloc[current_idx]):
                # 找到前一個 CVD 低點
                prev_cvd_low_idx = None
                for j in range(current_idx - 1, -1, -1):
                    if pd.notna(cvd_lows.iloc[j]):
                        bars_since = current_idx - j
                        if self.rangeLower <= bars_since <= self.rangeUpper:
                            prev_cvd_low_idx = j
                            break
                
                if prev_cvd_low_idx is not None:
                    # 檢查 CVD 更高低點
                    cvd_higher_low = cvd.iloc[current_idx] > cvd.iloc[prev_cvd_low_idx]
                    
                    # 檢查價格更低低點
                    price_lower_low = price_lows.iloc[current_idx] < price_lows.iloc[prev_cvd_low_idx]
                    
                    if cvd_higher_low and price_lower_low:
                        bull_signals.iloc[current_idx] = True
        
        # 檢測熊市背離 (價格更高高點，CVD 更低高點)
        for i in range(self.lbL + self.lbR, len(df)):
            current_idx = i - self.lbR
            
            if pd.notna(cvd_highs.iloc[current_idx]):
                # 找到前一個 CVD 高點
                prev_cvd_high_idx = None
                for j in range(current_idx - 1, -1, -1):
                    if pd.notna(cvd_highs.iloc[j]):
                        bars_since = current_idx - j
                        if self.rangeLower <= bars_since <= self.rangeUpper:
                            prev_cvd_high_idx = j
                            break
                
                if prev_cvd_high_idx is not None:
                    # 檢查 CVD 更低高點
                    cvd_lower_high = cvd.iloc[current_idx] < cvd.iloc[prev_cvd_high_idx]
                    
                    # 檢查價格更高高點
                    price_higher_high = price_highs.iloc[current_idx] > price_highs.iloc[prev_cvd_high_idx]
                    
                    if cvd_lower_high and price_higher_high:
                        bear_signals.iloc[current_idx] = True
        
        return bull_signals, bear_signals
    
    def calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """計算 ATR (Average True Range)"""
        high = df['High']
        low = df['Low']
        close = df['Close']
        
        # 計算 True Range
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        
        # 計算 ATR
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    def generate_signals_with_atr(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成帶 ATR 止盈止損的交易信號"""
        # 檢測背離
        bull_signals, bear_signals = self.detect_divergences(df)
        
        # 計算 ATR
        atr = self.calculate_atr(df)
        
        # 創建信號 DataFrame
        signals_df = pd.DataFrame(index=df.index)
        signals_df['Close'] = df['Close']
        signals_df['High'] = df['High']
        signals_df['Low'] = df['Low']
        signals_df['Volume'] = df['Volume']
        signals_df['ATR'] = atr
        
        # CVD 計算
        signals_df['CVD'] = self.calculate_cumulative_volume_delta(df)
        
        # 信號
        signals_df['Bull_Signal'] = bull_signals
        signals_df['Bear_Signal'] = bear_signals
        
        # 計算進場價格和止盈止損
        signals_df['Entry_Price'] = np.nan
        signals_df['Take_Profit'] = np.nan
        signals_df['Stop_Loss'] = np.nan
        signals_df['Signal_Type'] = ''
        
        # 處理牛市信號
        bull_indices = signals_df[signals_df['Bull_Signal']].index
        for idx in bull_indices:
            if idx in signals_df.index:
                entry_price = signals_df.loc[idx, 'Close']
                atr_value = signals_df.loc[idx, 'ATR']
                
                if pd.notna(atr_value):
                    signals_df.loc[idx, 'Entry_Price'] = entry_price
                    signals_df.loc[idx, 'Take_Profit'] = entry_price + (1.5 * atr_value)
                    signals_df.loc[idx, 'Stop_Loss'] = entry_price - (1.0 * atr_value)
                    signals_df.loc[idx, 'Signal_Type'] = 'LONG'
        
        # 處理熊市信號
        bear_indices = signals_df[signals_df['Bear_Signal']].index
        for idx in bear_indices:
            if idx in signals_df.index:
                entry_price = signals_df.loc[idx, 'Close']
                atr_value = signals_df.loc[idx, 'ATR']
                
                if pd.notna(atr_value):
                    signals_df.loc[idx, 'Entry_Price'] = entry_price
                    signals_df.loc[idx, 'Take_Profit'] = entry_price - (1.5 * atr_value)
                    signals_df.loc[idx, 'Stop_Loss'] = entry_price + (1.0 * atr_value)
                    signals_df.loc[idx, 'Signal_Type'] = 'SHORT'
        
        return signals_df
    
    def plot_signals(self, df: pd.DataFrame, signals_df: pd.DataFrame, start_date: str = None, end_date: str = None):
        """繪製價格圖表和信號"""
        # 篩選日期範圍
        if start_date and end_date:
            mask = (signals_df.index >= start_date) & (signals_df.index <= end_date)
            plot_df = signals_df[mask]
            price_df = df[mask]
        else:
            plot_df = signals_df.tail(500)  # 顯示最近 500 個數據點
            price_df = df.tail(500)
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12))
        
        # 價格圖表
        ax1.plot(plot_df.index, plot_df['Close'], label='Price', linewidth=1)
        
        # 標記信號
        bull_signals = plot_df[plot_df['Bull_Signal']]
        bear_signals = plot_df[plot_df['Bear_Signal']]
        
        if not bull_signals.empty:
            ax1.scatter(bull_signals.index, bull_signals['Close'], 
                       color='green', marker='^', s=100, label='Bull Signal', zorder=5)
        
        if not bear_signals.empty:
            ax1.scatter(bear_signals.index, bear_signals['Close'], 
                       color='red', marker='v', s=100, label='Bear Signal', zorder=5)
        
        ax1.set_title('CVD Divergence Signals', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Price')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # CVD 圖表
        ax2.plot(plot_df.index, plot_df['CVD'], label='CVD', color='purple', linewidth=1)
        ax2.set_title('Cumulative Volume Delta', fontsize=14, fontweight='bold')
        ax2.set_ylabel('CVD')
        ax2.set_xlabel('Time')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('cvd_divergence_signals.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ CVD 背離信號圖表已保存: cvd_divergence_signals.png")

def test_cvd_indicator():
    """測試 CVD 指標"""
    print("🧪 測試 CVD 背離指標...")
    
    # 載入測試數據
    try:
        df = pd.read_csv('binance_data/BTCUSDT_15m_enhanced.csv', index_col=0, parse_dates=True)
        print(f"✅ 載入數據成功: {len(df)} 條記錄")
    except Exception as e:
        print(f"❌ 數據載入失敗: {e}")
        return
    
    # 創建 CVD 指標
    cvd_indicator = CVDDivergenceIndicator()
    
    # 生成信號
    signals_df = cvd_indicator.generate_signals_with_atr(df)
    
    # 統計信號
    bull_count = signals_df['Bull_Signal'].sum()
    bear_count = signals_df['Bear_Signal'].sum()
    total_signals = bull_count + bear_count
    
    print(f"\n📊 信號統計:")
    print(f"  牛市信號 (Bull): {bull_count}")
    print(f"  熊市信號 (Bear): {bear_count}")
    print(f"  總信號數: {total_signals}")
    
    if total_signals > 0:
        print(f"  信號頻率: {len(df) / total_signals:.1f} 根K線/信號")
    
    # 繪製圖表
    cvd_indicator.plot_signals(df, signals_df)
    
    # 保存信號數據
    signal_records = signals_df[signals_df['Bull_Signal'] | signals_df['Bear_Signal']].copy()
    signal_records.to_csv('cvd_signals.csv')
    print(f"✅ 信號記錄已保存: cvd_signals.csv ({len(signal_records)} 條信號)")
    
    return signals_df

if __name__ == "__main__":
    test_cvd_indicator()
