# 🚀 CVD 策略雲端報單系統

基於 Cumulative Volume Delta (CVD) 背離檢測的自動化交易信號系統，支持多幣種監控和 Telegram 實時通知。

## 📊 系統特點

- **高勝率策略**: 經過 8 個幣種驗證，平均勝率 76.4%
- **低風險回撤**: 平均最大回撤僅 0.7%
- **實時監控**: 15分鐘級別實時信號檢測
- **自動通知**: Telegram 機器人實時推送交易信號
- **完整追蹤**: 自動監控每筆交易的止盈止損結果
- **每日報告**: 自動生成交易統計和績效報告

## 🎯 支持幣種

- **1000PEPE/USDT** - 平均收益率 87.4%
- **TRB/USDT** - 平均收益率 69.6%
- **DOGE/USDT** - 平均收益率 57.2%
- **SOL/USDT** - 平均收益率 49.1%
- **BTC/USDT** - 平均收益率 21.5%

## 📈 策略參數

- **時間框架**: 15分鐘
- **盈虧比**: 2:1 (2.0 ATR 止盈 : 1.0 ATR 止損)
- **信號類型**: CVD 背離檢測 (牛市/熊市背離)
- **監控頻率**: 每 5 分鐘掃描一次

## 🏗️ 系統架構

```
CVD 策略系統
├── 信號生成模塊 (cvd_strategy_core.py)
├── 數據庫管理 (database.py)
├── 交易監控 (trade_monitor.py)
├── Telegram 通知 (telegram_bot.py)
└── 主控制器 (cvd_main.py)
```

## 🚀 快速部署

### 1. 環境準備

```bash
# 克隆項目
git clone https://github.com/YCRicky/CVD_STR.git
cd CVD_STR

# 安裝依賴
pip install -r cvd_requirements.txt
```

### 2. 配置環境變量

```bash
# 複製環境變量模板
cp cvd_env_example .env

# 編輯環境變量
nano .env
```

設置以下變量：
```
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id
```

### 3. 運行系統

```bash
# 直接運行
python cvd_main.py

# 或使用 Docker
docker-compose -f cvd_docker_compose.yml up -d
```

## 📱 Telegram 消息格式

### 交易信號
```
⚡合約預言機 

💰 幣種: BTC/USDT
📈 方向: LONG
💵 入場價: $95,234.5600

🎯 止盈價: $96,789.1200
🛑 止損價: $93,679.9900
```

### 交易結算
```
🛑 交易結算

🟢 幣種: BTC/USDT
⏰ 時框: 15m
📈 方向: LONG

💵 入場價: $95,234.5600
💰 出場價: $96,789.1200
📊 結算類型: 止盈 

💸 盈虧: *****% 
⏰ 持倉時間: 2h 15m
```

### 每日報告
```
📊 每日交易總結 - 截至2025-07-25 

🎯 累計總信號數: 47 
✅ 止盈次數: 36
❌ 止損次數: 11
📈 勝率: 76.6%
💰 累計總盈虧: +234.56% 

系統將繼續監控新的交易信號。
```

## 🔧 系統功能

### 核心功能
- ✅ **實時信號檢測**: 基於 CVD 背離算法
- ✅ **多幣種監控**: 同時監控 5 個主流幣種
- ✅ **自動止盈止損**: ATR 動態止損機制
- ✅ **Telegram 通知**: 實時推送交易信號和結果
- ✅ **數據持久化**: SQLite 數據庫存儲所有交易記錄
- ✅ **每日統計**: 自動生成績效報告

### 高級功能
- 🔄 **並行處理**: 信號生成和交易監控獨立運行
- 📊 **實時監控**: 每分鐘檢查交易狀態
- 🛡️ **錯誤恢復**: 自動重試和異常處理
- 🧹 **數據清理**: 自動清理過期數據
- 📈 **性能統計**: 詳細的交易統計和分析

## 📋 API 依賴

- **Binance Futures API**: 獲取實時價格和K線數據
- **Telegram Bot API**: 發送通知消息

## ⚠️ 風險提示

1. **歷史表現不代表未來結果**
2. **加密貨幣交易存在高風險**
3. **請做好充分的風險管理**
4. **建議先小額測試驗證**

## 📞 技術支持

如有問題，請提交 Issue 或聯繫開發團隊。

## 📄 許可證

本項目僅供學習和研究使用，不構成投資建議。

---

**⚡ 開始您的量化交易之旅！**
