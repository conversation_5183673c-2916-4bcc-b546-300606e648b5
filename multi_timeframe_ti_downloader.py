"""
多時框 Taker Intensity 數據下載器
支持 5分鐘、15分鐘、1小時三個時框的數據下載

作者: 專業量化策略工程師
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import json
from typing import Dict, List, Optional
import logging

class MultiTimeframeTIDownloader:
    """多時框 Taker Intensity 數據下載器"""
    
    def __init__(self):
        # API配置
        self.base_url = "https://api.blave.org"
        self.headers = {
            "api-key": "acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6",
            "secret-key": "5dc330fd5a40ca402111b7774266fc5c32d0941e77125a6de7956fce68b12f0d"
        }
        
        # 時框配置
        self.timeframes = {
            '5min': {
                'period': '5min',
                'window': 12,
                'description': '5分鐘時框'
            },
            '15min': {
                'period': '15min', 
                'window': 16,
                'description': '15分鐘時框'
            },
            '1h': {
                'period': '1h',
                'window': 24,
                'description': '1小時時框'
            }
        }
        
        # 設置日誌
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 創建數據存儲目錄
        self.data_dir = "multi_timeframe_ti_data"
        os.makedirs(self.data_dir, exist_ok=True)
        
        self.session = None
    
    async def __aenter__(self):
        """異步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """異步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def get_ti_data(self, timeframe: str, symbol: str = "BTCUSDT", 
                         start_date: str = "2023-12-31", 
                         end_date: str = "2025-06-30") -> Optional[pd.DataFrame]:
        """獲取指定時框的 Taker Intensity 數據"""
        try:
            period = self.timeframes[timeframe]['period']
            description = self.timeframes[timeframe]['description']
            
            self.logger.info(f"🔗 獲取 {description} Taker Intensity 數據")
            
            url = f"{self.base_url}/taker_intensity/get_alpha"
            params = {
                "symbol": symbol,
                "period": period,
                "start_date": start_date,
                "end_date": end_date
            }
            
            self.logger.info(f"📋 API參數: {params}")
            
            async with self.session.get(url, headers=self.headers, params=params, timeout=120) as response:
                response_text = await response.text()
                self.logger.info(f"📡 API響應狀態: {response.status}")
                
                if response.status == 200:
                    try:
                        data = await response.json()
                        self.logger.info(f"📊 響應數據結構: {list(data.keys()) if isinstance(data, dict) else 'Not dict'}")
                        
                        # 解析 Blave API 響應格式
                        if "data" in data and isinstance(data["data"], dict):
                            data_content = data["data"]
                            
                            if "timestamp" in data_content and "alpha" in data_content:
                                timestamps = data_content["timestamp"]
                                alpha_values = data_content["alpha"]
                                
                                if timestamps and alpha_values and len(timestamps) == len(alpha_values):
                                    df = pd.DataFrame({
                                        'timestamp': [datetime.fromtimestamp(ts) for ts in timestamps],
                                        'taker_intensity': alpha_values
                                    })
                                    df.set_index('timestamp', inplace=True)
                                    df = df.sort_index()
                                    
                                    # 數據質量檢查
                                    self.logger.info(f"✅ {description} 數據獲取成功: {len(df)} 條記錄")
                                    self.logger.info(f"   時間範圍: {df.index.min()} 到 {df.index.max()}")
                                    self.logger.info(f"   TI範圍: {df['taker_intensity'].min():.4f} 到 {df['taker_intensity'].max():.4f}")
                                    self.logger.info(f"   TI均值: {df['taker_intensity'].mean():.4f}")
                                    
                                    return df
                                else:
                                    self.logger.error(f"❌ {description} 數據長度不匹配")
                            else:
                                self.logger.error(f"❌ {description} 響應缺少必要字段")
                        else:
                            self.logger.error(f"❌ {description} 響應格式錯誤")
                            
                    except Exception as json_error:
                        self.logger.error(f"❌ {description} JSON解析失敗: {json_error}")
                        self.logger.error(f"原始響應: {response_text[:500]}")
                else:
                    self.logger.error(f"❌ {description} API響應異常: {response.status}")
                    self.logger.error(f"響應內容: {response_text[:500]}")
                
                return None
                
        except Exception as e:
            self.logger.error(f"❌ {timeframe} Taker Intensity 數據獲取失敗: {e}")
            return None
    
    async def download_all_timeframes(self, symbol: str = "BTCUSDT",
                                     start_date: str = "2023-12-31",
                                     end_date: str = "2025-06-30") -> Dict[str, pd.DataFrame]:
        """下載所有時框的 Taker Intensity 數據"""
        self.logger.info(f"🚀 開始下載多時框 Taker Intensity 數據: {symbol}")
        self.logger.info(f"📅 時間範圍: {start_date} 到 {end_date}")
        
        all_ti_data = {}
        
        for timeframe in self.timeframes.keys():
            self.logger.info(f"\n📥 正在下載: {self.timeframes[timeframe]['description']}")
            
            ti_data = await self.get_ti_data(
                timeframe=timeframe,
                symbol=symbol,
                start_date=start_date,
                end_date=end_date
            )
            
            if ti_data is not None:
                all_ti_data[timeframe] = ti_data
                
                # 保存到文件
                filename = f"{self.data_dir}/TI_{symbol}_{timeframe}.csv"
                ti_data.to_csv(filename)
                self.logger.info(f"💾 {timeframe} 數據已保存到: {filename}")
            else:
                self.logger.warning(f"⚠️ {timeframe} 數據獲取失敗")
            
            # 避免請求過於頻繁
            await asyncio.sleep(2)
        
        self.logger.info(f"\n✅ 多時框數據下載完成，成功獲取 {len(all_ti_data)}/{len(self.timeframes)} 個時框")
        return all_ti_data
    
    def calculate_ti_bollinger_bands(self, ti_data: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """計算 Taker Intensity 布林帶指標"""
        try:
            window = self.timeframes[timeframe]['window']
            description = self.timeframes[timeframe]['description']
            
            self.logger.info(f"📊 計算 {description} TI布林帶指標 (窗口={window})")
            
            # 創建副本
            df = ti_data.copy()
            
            # 計算滾動統計
            rolling_stats = df['taker_intensity'].rolling(window=window)
            
            # 計算布林帶軌道
            df['ti_middle'] = rolling_stats.mean()  # 中軌：均值
            df['ti_upper'] = rolling_stats.quantile(0.70)  # 上軌：70分位數
            df['ti_lower'] = rolling_stats.quantile(0.30)  # 下軌：30分位數
            
            # 計算相對位置
            df['ti_position'] = (df['taker_intensity'] - df['ti_lower']) / (df['ti_upper'] - df['ti_lower'])
            
            # 計算突破信號
            df['upper_breakout'] = (df['taker_intensity'] > df['ti_upper']).astype(int)
            df['lower_breakout'] = (df['taker_intensity'] < df['ti_lower']).astype(int)
            
            # 移除初始的NaN值
            df = df.dropna()
            
            self.logger.info(f"✅ {description} TI布林帶計算完成: {len(df)} 條有效記錄")
            self.logger.info(f"   上軌突破次數: {df['upper_breakout'].sum()}")
            self.logger.info(f"   下軌突破次數: {df['lower_breakout'].sum()}")
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ {timeframe} TI布林帶計算失敗: {e}")
            return ti_data
    
    def save_summary(self, all_ti_data: Dict[str, pd.DataFrame], symbol: str = "BTCUSDT"):
        """保存數據摘要"""
        summary = {
            "symbol": symbol,
            "download_time": datetime.now().isoformat(),
            "timeframes": {}
        }
        
        for timeframe, data in all_ti_data.items():
            config = self.timeframes[timeframe]
            summary["timeframes"][timeframe] = {
                "description": config['description'],
                "period": config['period'],
                "window": config['window'],
                "records_count": len(data),
                "date_range": {
                    "start": data.index.min().isoformat(),
                    "end": data.index.max().isoformat()
                },
                "ti_stats": {
                    "mean": float(data['taker_intensity'].mean()),
                    "std": float(data['taker_intensity'].std()),
                    "min": float(data['taker_intensity'].min()),
                    "max": float(data['taker_intensity'].max())
                },
                "columns": list(data.columns)
            }
        
        summary_file = f"{self.data_dir}/multi_timeframe_summary_{symbol}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"📋 多時框數據摘要已保存到: {summary_file}")
        return summary

async def main():
    """主函數 - 下載多時框 Taker Intensity 數據"""
    async with MultiTimeframeTIDownloader() as downloader:
        # 下載所有時框的 TI 數據
        all_data = await downloader.download_all_timeframes(
            symbol="BTCUSDT",
            start_date="2023-12-31",
            end_date="2025-06-30"
        )
        
        # 計算每個時框的布林帶指標
        enhanced_data = {}
        for timeframe, ti_data in all_data.items():
            enhanced_data[timeframe] = downloader.calculate_ti_bollinger_bands(ti_data, timeframe)
            
            # 保存增強數據
            enhanced_filename = f"{downloader.data_dir}/TI_Enhanced_{timeframe}_BTCUSDT.csv"
            enhanced_data[timeframe].to_csv(enhanced_filename)
            downloader.logger.info(f"💾 {timeframe} 增強數據已保存到: {enhanced_filename}")
        
        # 保存摘要
        summary = downloader.save_summary(enhanced_data, "BTCUSDT")
        
        print("\n" + "="*80)
        print("📊 多時框 Taker Intensity 數據下載完成!")
        print("="*80)
        print(f"成功下載時框數量: {len(enhanced_data)}")
        
        for timeframe, data in enhanced_data.items():
            config = downloader.timeframes[timeframe]
            print(f"\n✅ {config['description']}:")
            print(f"   記錄數量: {len(data):,}")
            print(f"   時間範圍: {data.index.min()} 到 {data.index.max()}")
            print(f"   TI範圍: {data['taker_intensity'].min():.4f} 到 {data['taker_intensity'].max():.4f}")
            print(f"   上軌突破: {data['upper_breakout'].sum()} 次")
            print(f"   下軌突破: {data['lower_breakout'].sum()} 次")
        
        print("="*80)

if __name__ == "__main__":
    asyncio.run(main())
