"""
Telegram 機器人系統
處理信號發送、交易追蹤、每日報告

作者: 專業量化策略工程師
"""

import asyncio
import aiohttp
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging
import os
from database import CVDDatabase, TradeResult
from cvd_strategy_core import TradingSignal
from trade_monitor import TradeMonitor

class TelegramBot:
    """Telegram 機器人"""
    
    def __init__(self, bot_token: str, chat_id: str, db: CVDDatabase):
        self.bot_token = bot_token
        self.chat_id = chat_id
        self.db = db
        self.base_url = f"https://api.telegram.org/bot{bot_token}"
        
        # 設置日誌
        self.logger = logging.getLogger(__name__)
        
        self.session = None
    
    async def __aenter__(self):
        """異步上下文管理器"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """異步上下文管理器"""
        if self.session:
            await self.session.close()
    
    async def send_message(self, message: str, parse_mode: str = "HTML") -> bool:
        """發送消息到Telegram"""
        try:
            url = f"{self.base_url}/sendMessage"
            data = {
                "chat_id": self.chat_id,
                "text": message,
                "parse_mode": parse_mode
            }
            
            async with self.session.post(url, json=data, timeout=30) as response:
                if response.status == 200:
                    self.logger.info("✅ Telegram消息發送成功")
                    return True
                else:
                    error_text = await response.text()
                    self.logger.error(f"❌ Telegram消息發送失敗: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"❌ Telegram消息發送異常: {e}")
            return False
    
    async def send_signal(self, signal: TradingSignal) -> bool:
        """發送交易信號"""
        try:
            # 格式化信號消息
            symbol_display = signal.symbol.replace("USDT", "/USDT").replace("1000", "")
            
            message = f"""⚡合約預言機 

💰 幣種: {symbol_display}
📈 方向: {signal.direction}
💵 入場價: ${signal.entry_price:.4f}

🎯 止盈價: ${signal.take_profit:.4f}
🛑 止損價: ${signal.stop_loss:.4f}"""
            
            success = await self.send_message(message)
            if success:
                self.logger.info(f"✅ 信號已發送: {signal.signal_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"❌ 信號發送失敗: {e}")
            return False
    
    async def send_trade_result(self, trade_result: TradeResult) -> bool:
        """發送交易結果"""
        try:
            symbol_display = trade_result.symbol.replace("USDT", "/USDT").replace("1000", "")
            
            # 結算類型中文
            result_type_cn = "止盈" if trade_result.result_type == "TAKE_PROFIT" else "止損"
            
            # 盈虧顏色
            pnl_color = "🟢" if trade_result.pnl_percentage > 0 else "🔴"
            
            # 格式化持倉時間
            duration_str = self.format_duration(trade_result.duration_minutes)
            
            message = f"""🛑 交易結算

{pnl_color} 幣種: {symbol_display}
⏰ 時框: 15m
📈 方向: {trade_result.direction}

💵 入場價: ${trade_result.entry_price:.4f}
💰 出場價: ${trade_result.exit_price:.4f}
📊 結算類型: {result_type_cn} 

💸 盈虧: {trade_result.pnl_percentage:+.2f}% 
⏰ 持倉時間: {duration_str}"""
            
            success = await self.send_message(message)
            if success:
                self.logger.info(f"✅ 交易結果已發送: {trade_result.signal_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"❌ 交易結果發送失敗: {e}")
            return False
    
    def format_duration(self, minutes: int) -> str:
        """格式化持倉時間"""
        if minutes < 60:
            return f"{minutes}m"
        else:
            hours = minutes // 60
            mins = minutes % 60
            return f"{hours}h {mins}m"
    
    async def send_daily_report(self) -> bool:
        """發送每日報告"""
        try:
            # 獲取統計數據
            stats = self.db.get_trade_statistics()
            
            # 格式化日期
            today = datetime.now().strftime("%Y-%m-%d")
            
            message = f"""📊 每日交易總結 - 截至{today} 

🎯 累計總信號數: {stats['total_signals']} 
✅ 止盈次數: {stats['take_profit_count']}
❌ 止損次數: {stats['stop_loss_count']}
📈 勝率: {stats['win_rate']:.1%}
💰 累計總盈虧: {stats['total_pnl']:+.2f}% 

系統將繼續監控新的交易信號。"""
            
            success = await self.send_message(message)
            if success:
                self.logger.info("✅ 每日報告已發送")
            
            return success
            
        except Exception as e:
            self.logger.error(f"❌ 每日報告發送失敗: {e}")
            return False
    
    async def send_system_status(self) -> bool:
        """發送系統狀態"""
        try:
            # 獲取活躍信號數量
            active_signals = self.db.get_active_signals()
            
            # 獲取最近交易
            recent_trades = self.db.get_recent_trades(5)
            
            message = f"""🤖 系統狀態報告

⚡ 活躍信號: {len(active_signals)} 個
📊 最近5筆交易:"""
            
            for trade in recent_trades:
                symbol_display = trade['symbol'].replace("USDT", "/USDT").replace("1000", "")
                result_emoji = "✅" if trade['result_type'] == "TAKE_PROFIT" else "❌"
                message += f"\n{result_emoji} {symbol_display} {trade['direction']} {trade['pnl_percentage']:+.2f}%"
            
            if not recent_trades:
                message += "\n暫無交易記錄"
            
            message += f"\n\n🕐 更新時間: {datetime.now().strftime('%H:%M:%S')}"
            
            success = await self.send_message(message)
            if success:
                self.logger.info("✅ 系統狀態已發送")
            
            return success
            
        except Exception as e:
            self.logger.error(f"❌ 系統狀態發送失敗: {e}")
            return False

class TelegramNotificationService:
    """Telegram 通知服務"""
    
    def __init__(self, bot_token: str, chat_id: str, db: CVDDatabase):
        self.bot = None
        self.bot_token = bot_token
        self.chat_id = chat_id
        self.db = db
        
        # 設置日誌
        self.logger = logging.getLogger(__name__)
        
        # 消息隊列
        self.message_queue = asyncio.Queue()
        self.is_running = False
    
    async def start(self):
        """啟動通知服務"""
        self.logger.info("🚀 Telegram通知服務啟動")
        self.is_running = True
        
        async with TelegramBot(self.bot_token, self.chat_id, self.db) as bot:
            self.bot = bot
            
            # 啟動消息處理任務
            message_task = asyncio.create_task(self.process_messages())
            daily_report_task = asyncio.create_task(self.daily_report_scheduler())
            
            try:
                # 發送啟動消息
                await bot.send_message("🚀 CVD策略系統已啟動，開始監控交易信號...")
                
                # 等待任務完成
                await asyncio.gather(message_task, daily_report_task)
                
            except Exception as e:
                self.logger.error(f"❌ 通知服務異常: {e}")
            finally:
                self.is_running = False
    
    async def process_messages(self):
        """處理消息隊列"""
        while self.is_running:
            try:
                # 等待消息
                message_data = await asyncio.wait_for(self.message_queue.get(), timeout=1.0)
                
                message_type = message_data['type']
                data = message_data['data']
                
                if message_type == 'signal':
                    await self.bot.send_signal(data)
                elif message_type == 'trade_result':
                    await self.bot.send_trade_result(data)
                elif message_type == 'daily_report':
                    await self.bot.send_daily_report()
                elif message_type == 'system_status':
                    await self.bot.send_system_status()
                
                # 標記任務完成
                self.message_queue.task_done()
                
                # 避免發送過於頻繁
                await asyncio.sleep(1)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"❌ 消息處理異常: {e}")
    
    async def daily_report_scheduler(self):
        """每日報告調度器"""
        while self.is_running:
            try:
                now = datetime.now()
                
                # 每天12:00發送報告
                if now.hour == 12 and now.minute == 0:
                    await self.queue_message('daily_report', None)
                    
                    # 等待一分鐘避免重複發送
                    await asyncio.sleep(60)
                else:
                    # 每分鐘檢查一次
                    await asyncio.sleep(60)
                    
            except Exception as e:
                self.logger.error(f"❌ 每日報告調度異常: {e}")
                await asyncio.sleep(60)
    
    async def queue_message(self, message_type: str, data):
        """添加消息到隊列"""
        try:
            await self.message_queue.put({
                'type': message_type,
                'data': data,
                'timestamp': datetime.now()
            })
            self.logger.info(f"✅ 消息已加入隊列: {message_type}")
        except Exception as e:
            self.logger.error(f"❌ 消息入隊失敗: {e}")
    
    async def notify_signal(self, signal: TradingSignal):
        """通知新信號"""
        await self.queue_message('signal', signal)
    
    async def notify_trade_result(self, trade_result: TradeResult):
        """通知交易結果"""
        await self.queue_message('trade_result', trade_result)
    
    async def notify_system_status(self):
        """通知系統狀態"""
        await self.queue_message('system_status', None)

async def test_telegram_bot():
    """測試Telegram機器人"""
    print("🧪 測試Telegram機器人...")
    
    # 注意: 需要設置環境變量
    bot_token = os.getenv("TELEGRAM_BOT_TOKEN", "YOUR_BOT_TOKEN")
    chat_id = os.getenv("TELEGRAM_CHAT_ID", "YOUR_CHAT_ID")
    
    if bot_token == "YOUR_BOT_TOKEN" or chat_id == "YOUR_CHAT_ID":
        print("⚠️ 請設置 TELEGRAM_BOT_TOKEN 和 TELEGRAM_CHAT_ID 環境變量")
        return
    
    db = CVDDatabase("test_cvd.db")
    
    async with TelegramBot(bot_token, chat_id, db) as bot:
        # 測試發送消息
        success = await bot.send_message("🧪 測試消息")
        print(f"發送測試消息: {'✅' if success else '❌'}")
        
        # 測試發送每日報告
        success = await bot.send_daily_report()
        print(f"發送每日報告: {'✅' if success else '❌'}")

if __name__ == "__main__":
    asyncio.run(test_telegram_bot())
