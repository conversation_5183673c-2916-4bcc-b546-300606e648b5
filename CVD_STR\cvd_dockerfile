# CVD 策略 Docker 配置
FROM python:3.9-slim

# 設置工作目錄
WORKDIR /app

# 安裝系統依賴
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 複製依賴文件
COPY cvd_requirements.txt .

# 安裝 Python 依賴
RUN pip install --no-cache-dir -r cvd_requirements.txt

# 複製應用代碼
COPY cvd_strategy_core.py .
COPY database.py .
COPY trade_monitor.py .
COPY telegram_bot.py .
COPY cvd_main.py .

# 創建數據目錄
RUN mkdir -p /app/data

# 設置環境變量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 暴露端口（如果需要）
# EXPOSE 8000

# 運行應用
CMD ["python", "cvd_main.py"]
