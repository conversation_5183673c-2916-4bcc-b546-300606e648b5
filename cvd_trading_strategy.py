"""
CVD 背離交易策略回測系統
基於 Cumulative Volume Delta Divergence 信號進行交易

策略參數:
- 初始資金: 10,000 USDT
- 每次交易: 1% 資金
- 槓桿: 10x
- 止盈: 1.5 ATR
- 止損: 1.0 ATR
- 回測期間: 2年

作者: 專業量化策略工程師
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from cvd_divergence_indicator import CVDDivergenceIndicator
import warnings
warnings.filterwarnings('ignore')

class CVDTradingStrategy:
    """CVD 背離交易策略"""
    
    def __init__(self, 
                 initial_capital: float = 10000,
                 risk_per_trade: float = 0.01,
                 leverage: float = 10.0,
                 take_profit_atr: float = 1.5,
                 stop_loss_atr: float = 1.0):
        
        self.initial_capital = initial_capital
        self.risk_per_trade = risk_per_trade
        self.leverage = leverage
        self.take_profit_atr = take_profit_atr
        self.stop_loss_atr = stop_loss_atr
        
        # 交易記錄
        self.trades = []
        self.equity_curve = []
        
    def calculate_position_size(self, account_balance: float, entry_price: float, stop_loss: float) -> float:
        """計算倉位大小"""
        # 限制最大賬戶餘額以防止數值溢出
        max_balance = 1000000  # 100萬上限
        account_balance = min(account_balance, max_balance)

        # 1% 資金風險
        risk_amount = account_balance * self.risk_per_trade

        # 計算每單位風險
        price_risk = abs(entry_price - stop_loss) / entry_price  # 使用百分比風險

        if price_risk == 0 or price_risk > 0.1:  # 限制最大風險為10%
            return 0

        # 計算基礎倉位大小 (以USDT計價)
        position_value = risk_amount / price_risk

        # 限制單筆交易最大倉位
        max_position_value = account_balance * 0.1  # 最大10%資金
        position_value = min(position_value, max_position_value)

        # 轉換為幣數量
        position_size = position_value / entry_price

        return position_size
    
    def execute_trade(self, signal_row: pd.Series, next_bar: pd.Series, account_balance: float) -> dict:
        """執行單筆交易"""
        signal_type = signal_row['Signal_Type']
        entry_price = next_bar['Open']  # 下一根K線開盤價進場
        take_profit = signal_row['Take_Profit']
        stop_loss = signal_row['Stop_Loss']
        
        # 調整止盈止損基於實際進場價格
        atr_value = signal_row['ATR']
        if signal_type == 'LONG':
            take_profit = entry_price + (self.take_profit_atr * atr_value)
            stop_loss = entry_price - (self.stop_loss_atr * atr_value)
        else:  # SHORT
            take_profit = entry_price - (self.take_profit_atr * atr_value)
            stop_loss = entry_price + (self.stop_loss_atr * atr_value)
        
        # 計算倉位大小
        position_size = self.calculate_position_size(account_balance, entry_price, stop_loss)
        
        if position_size <= 0:
            return None
        
        trade = {
            'entry_time': next_bar.name,
            'signal_time': signal_row.name,
            'signal_type': signal_type,
            'entry_price': entry_price,
            'take_profit': take_profit,
            'stop_loss': stop_loss,
            'position_size': position_size,
            'account_balance': account_balance,
            'atr': atr_value,
            'status': 'OPEN'
        }
        
        return trade
    
    def check_trade_exit(self, trade: dict, current_bar: pd.Series) -> dict:
        """檢查交易是否應該平倉"""
        if trade['status'] != 'OPEN':
            return trade
        
        high = current_bar['High']
        low = current_bar['Low']
        close = current_bar['Close']
        
        exit_price = None
        exit_reason = None
        
        if trade['signal_type'] == 'LONG':
            # 多單檢查
            if high >= trade['take_profit']:
                exit_price = trade['take_profit']
                exit_reason = 'TAKE_PROFIT'
            elif low <= trade['stop_loss']:
                exit_price = trade['stop_loss']
                exit_reason = 'STOP_LOSS'
        else:  # SHORT
            # 空單檢查
            if low <= trade['take_profit']:
                exit_price = trade['take_profit']
                exit_reason = 'TAKE_PROFIT'
            elif high >= trade['stop_loss']:
                exit_price = trade['stop_loss']
                exit_reason = 'STOP_LOSS'
        
        if exit_price is not None:
            # 計算盈虧 (使用槓桿)
            price_change_pct = (exit_price - trade['entry_price']) / trade['entry_price']
            if trade['signal_type'] == 'SHORT':
                price_change_pct = -price_change_pct

            # 應用槓桿
            leveraged_return = price_change_pct * self.leverage

            # 計算實際盈虧 (基於投入資金)
            invested_amount = trade['account_balance'] * self.risk_per_trade
            pnl = invested_amount * leveraged_return

            # 限制單筆最大盈虧
            max_pnl = trade['account_balance'] * 0.5  # 最大50%
            pnl = max(-max_pnl, min(pnl, max_pnl))

            # 計算收益率
            pnl_percentage = pnl / trade['account_balance']

            trade.update({
                'exit_time': current_bar.name,
                'exit_price': exit_price,
                'exit_reason': exit_reason,
                'pnl': pnl,
                'pnl_percentage': pnl_percentage,
                'leveraged_return': leveraged_return,
                'status': 'CLOSED'
            })
        
        return trade
    
    def run_backtest(self, df: pd.DataFrame, start_date: str = None, end_date: str = None) -> dict:
        """運行回測"""
        print("🚀 開始 CVD 背離策略回測...")
        
        # 篩選回測期間
        if start_date and end_date:
            mask = (df.index >= start_date) & (df.index <= end_date)
            backtest_df = df[mask].copy()
        else:
            # 默認使用最近2年數據
            backtest_df = df.tail(int(365 * 24 * 4 * 2)).copy()  # 2年的15分鐘數據
        
        print(f"📅 回測期間: {backtest_df.index[0]} 到 {backtest_df.index[-1]}")
        print(f"📊 數據量: {len(backtest_df)} 條記錄")
        
        # 生成 CVD 信號
        cvd_indicator = CVDDivergenceIndicator()
        signals_df = cvd_indicator.generate_signals_with_atr(backtest_df)
        
        # 初始化
        account_balance = self.initial_capital
        open_trades = []
        closed_trades = []
        equity_history = []
        
        # 獲取所有信號
        signal_indices = signals_df[signals_df['Bull_Signal'] | signals_df['Bear_Signal']].index
        
        print(f"🎯 檢測到 {len(signal_indices)} 個交易信號")
        
        # 逐根K線處理
        for i, (current_time, current_bar) in enumerate(backtest_df.iterrows()):
            
            # 檢查現有交易的平倉條件
            for trade in open_trades[:]:
                updated_trade = self.check_trade_exit(trade, current_bar)
                if updated_trade['status'] == 'CLOSED':
                    closed_trades.append(updated_trade)
                    open_trades.remove(trade)
                    
                    # 更新賬戶餘額
                    account_balance += updated_trade['pnl']
            
            # 檢查是否有新信號
            if current_time in signal_indices:
                signal_row = signals_df.loc[current_time]
                
                # 確保有下一根K線用於進場
                next_index = i + 1
                if next_index < len(backtest_df):
                    next_bar = backtest_df.iloc[next_index]
                    
                    # 執行交易
                    new_trade = self.execute_trade(signal_row, next_bar, account_balance)
                    if new_trade:
                        open_trades.append(new_trade)
            
            # 記錄權益曲線
            total_unrealized_pnl = 0
            for trade in open_trades:
                if trade['signal_type'] == 'LONG':
                    unrealized_pnl = (current_bar['Close'] - trade['entry_price']) * trade['position_size']
                else:
                    unrealized_pnl = (trade['entry_price'] - current_bar['Close']) * trade['position_size']
                total_unrealized_pnl += unrealized_pnl
            
            current_equity = account_balance + total_unrealized_pnl
            equity_history.append({
                'time': current_time,
                'equity': current_equity,
                'balance': account_balance,
                'unrealized_pnl': total_unrealized_pnl,
                'open_trades': len(open_trades)
            })
        
        # 強制平倉所有未平倉交易
        final_bar = backtest_df.iloc[-1]
        for trade in open_trades:
            # 使用相同的盈虧計算邏輯
            price_change_pct = (final_bar['Close'] - trade['entry_price']) / trade['entry_price']
            if trade['signal_type'] == 'SHORT':
                price_change_pct = -price_change_pct

            leveraged_return = price_change_pct * self.leverage
            invested_amount = trade['account_balance'] * self.risk_per_trade
            pnl = invested_amount * leveraged_return

            # 限制最大盈虧
            max_pnl = trade['account_balance'] * 0.5
            pnl = max(-max_pnl, min(pnl, max_pnl))

            trade.update({
                'exit_time': final_bar.name,
                'exit_price': final_bar['Close'],
                'exit_reason': 'FORCE_CLOSE',
                'pnl': pnl,
                'pnl_percentage': pnl / trade['account_balance'],
                'leveraged_return': leveraged_return,
                'status': 'CLOSED'
            })
            closed_trades.append(trade)
            account_balance += pnl
        
        # 保存結果
        self.trades = closed_trades
        self.equity_curve = equity_history
        
        # 計算績效指標
        performance = self.calculate_performance_metrics()
        
        print(f"\n📈 回測完成!")
        print(f"  總交易次數: {len(closed_trades)}")
        print(f"  最終資金: ${account_balance:,.2f}")
        print(f"  總收益率: {((account_balance / self.initial_capital) - 1) * 100:.2f}%")
        
        return performance
    
    def calculate_performance_metrics(self) -> dict:
        """計算績效指標"""
        if not self.trades:
            return {}
        
        trades_df = pd.DataFrame(self.trades)
        equity_df = pd.DataFrame(self.equity_curve)
        
        # 基本統計
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['pnl'] > 0])
        losing_trades = len(trades_df[trades_df['pnl'] < 0])
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # 盈虧統計
        total_pnl = trades_df['pnl'].sum()
        avg_win = trades_df[trades_df['pnl'] > 0]['pnl'].mean() if winning_trades > 0 else 0
        avg_loss = trades_df[trades_df['pnl'] < 0]['pnl'].mean() if losing_trades > 0 else 0
        
        profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if losing_trades > 0 and avg_loss != 0 else float('inf')
        
        # 收益率統計
        final_equity = equity_df['equity'].iloc[-1]
        total_return = (final_equity / self.initial_capital - 1) * 100
        
        # 最大回撤
        equity_series = pd.Series(equity_df['equity'].values, index=pd.to_datetime(equity_df['time']))
        rolling_max = equity_series.expanding().max()
        drawdown = (equity_series - rolling_max) / rolling_max * 100
        max_drawdown = drawdown.min()
        
        # Sharpe 比率 (簡化版)
        daily_returns = equity_series.resample('D').last().pct_change().dropna()
        if len(daily_returns) > 1 and daily_returns.std() != 0:
            sharpe_ratio = daily_returns.mean() / daily_returns.std() * np.sqrt(365)
        else:
            sharpe_ratio = 0
        
        performance = {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'total_return': total_return,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'final_equity': final_equity
        }
        
        return performance
    
    def plot_results(self):
        """繪製回測結果"""
        if not self.equity_curve:
            print("❌ 沒有回測數據可繪製")
            return
        
        equity_df = pd.DataFrame(self.equity_curve)
        equity_df['time'] = pd.to_datetime(equity_df['time'])
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12))
        
        # 權益曲線
        ax1.plot(equity_df['time'], equity_df['equity'], label='Total Equity', linewidth=2)
        ax1.plot(equity_df['time'], equity_df['balance'], label='Cash Balance', linewidth=1, alpha=0.7)
        ax1.axhline(y=self.initial_capital, color='gray', linestyle='--', alpha=0.5, label='Initial Capital')
        
        ax1.set_title('CVD Strategy - Equity Curve', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Account Value (USDT)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 回撤圖
        equity_series = pd.Series(equity_df['equity'].values, index=equity_df['time'])
        rolling_max = equity_series.expanding().max()
        drawdown = (equity_series - rolling_max) / rolling_max * 100
        
        ax2.fill_between(equity_df['time'], drawdown, 0, alpha=0.3, color='red')
        ax2.plot(equity_df['time'], drawdown, color='red', linewidth=1)
        ax2.set_title('Drawdown', fontsize=14, fontweight='bold')
        ax2.set_ylabel('Drawdown (%)')
        ax2.set_xlabel('Time')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('cvd_strategy_results.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 回測結果圖表已保存: cvd_strategy_results.png")
    
    def save_results(self):
        """保存回測結果"""
        if self.trades:
            trades_df = pd.DataFrame(self.trades)
            trades_df.to_csv('cvd_strategy_trades.csv', index=False)
            print(f"✅ 交易記錄已保存: cvd_strategy_trades.csv ({len(trades_df)} 筆交易)")
        
        if self.equity_curve:
            equity_df = pd.DataFrame(self.equity_curve)
            equity_df.to_csv('cvd_strategy_equity.csv', index=False)
            print(f"✅ 權益曲線已保存: cvd_strategy_equity.csv")

def main():
    """主函數 - 運行 CVD 策略回測"""
    print("🚀 CVD 背離交易策略回測系統")
    print("=" * 60)
    
    # 載入數據
    try:
        df = pd.read_csv('binance_data/BTCUSDT_15m_enhanced.csv', index_col=0, parse_dates=True)
        print(f"✅ 數據載入成功: {len(df)} 條記錄")
    except Exception as e:
        print(f"❌ 數據載入失敗: {e}")
        return
    
    # 創建策略
    strategy = CVDTradingStrategy(
        initial_capital=10000,
        risk_per_trade=0.01,
        leverage=10.0,
        take_profit_atr=1.5,
        stop_loss_atr=1.0
    )
    
    # 運行回測
    performance = strategy.run_backtest(df)
    
    # 顯示結果
    if performance:
        print(f"\n📊 績效報告:")
        print(f"  總交易次數: {performance['total_trades']}")
        print(f"  勝率: {performance['win_rate']:.2%}")
        print(f"  總收益率: {performance['total_return']:.2f}%")
        print(f"  最大回撤: {performance['max_drawdown']:.2f}%")
        print(f"  盈虧比: {performance['profit_factor']:.2f}")
        print(f"  Sharpe比率: {performance['sharpe_ratio']:.2f}")
        print(f"  平均盈利: ${performance['avg_win']:.2f}")
        print(f"  平均虧損: ${performance['avg_loss']:.2f}")
    
    # 繪製和保存結果
    strategy.plot_results()
    strategy.save_results()
    
    print("\n🎉 CVD 策略回測完成!")

if __name__ == "__main__":
    main()
