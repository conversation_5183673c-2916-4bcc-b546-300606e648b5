"""
Blave 全因子數據下載器
下載所有 Blave API 支持的因子數據進行量化分析

支持的因子:
- HC: Holder Concentration (持倉集中度)
- WH: Whale Hunter (鯨魚監控)
- UM: Unusual Movement (異動偵測)
- TI: Taker Intensity (吃單力度)
- SR: Sector Rotation (板塊輪動)
- MS: Market Sentiment (市場情緒)
- SM: Squeeze Momentum (擠壓動量)
- CS: Capital Shortage (資金短缺)
- BT: Blave Top Traders (頂尖交易者)
- FR: Funding Rate (資金費率)

作者: 專業量化策略工程師
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import json
from typing import Dict, List, Optional
import logging

class BlaveFactorDownloader:
    """Blave 全因子數據下載器"""
    
    def __init__(self):
        # API配置
        self.base_url = "https://api.blave.org"
        self.headers = {
            "api-key": "acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6",
            "secret-key": "5dc330fd5a40ca402111b7774266fc5c32d0941e77125a6de7956fce68b12f0d"
        }
        
        # 支持的因子列表
        self.factors = {
            'holder_concentration': 'HC - 持倉集中度',
            'whale_hunter': 'WH - 鯨魚監控',
            'unusual_movement': 'UM - 異動偵測',
            'taker_intensity': 'TI - 吃單力度',
            'sector_rotation': 'SR - 板塊輪動',
            'market_sentiment': 'MS - 市場情緒',
            'squeeze_momentum': 'SM - 擠壓動量',
            'capital_shortage': 'CS - 資金短缺',
            'blave_top_traders': 'BT - 頂尖交易者',
            'funding_rate': 'FR - 資金費率'
        }
        
        # 設置日誌
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 創建數據存儲目錄
        self.data_dir = "blave_factor_data"
        os.makedirs(self.data_dir, exist_ok=True)
        
        self.session = None
    
    async def __aenter__(self):
        """異步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """異步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def get_factor_data(self, factor_name: str, symbol: str = "BTCUSDT", 
                             period: str = "15min", start_date: str = None, 
                             end_date: str = None) -> Optional[pd.DataFrame]:
        """獲取指定因子的數據"""
        try:
            url = f"{self.base_url}/{factor_name}/get_alpha"
            params = {
                "symbol": symbol,
                "period": period
            }
            
            # 添加時間範圍參數（如果API支持）
            if start_date:
                params["start_date"] = start_date
            if end_date:
                params["end_date"] = end_date
            
            self.logger.info(f"🔗 獲取 {factor_name} 數據: {url}")
            self.logger.info(f"📋 參數: {params}")
            
            async with self.session.get(url, headers=self.headers, params=params, timeout=60) as response:
                response_text = await response.text()
                self.logger.info(f"📡 {factor_name} API響應狀態: {response.status}")
                
                if response.status == 200:
                    try:
                        data = await response.json()
                        self.logger.info(f"📊 {factor_name} 響應數據結構: {list(data.keys()) if isinstance(data, dict) else 'Not dict'}")
                        
                        # 解析 Blave API 響應格式
                        if "data" in data and isinstance(data["data"], dict):
                            data_content = data["data"]
                            
                            # 檢查數據格式
                            if "timestamp" in data_content and "alpha" in data_content:
                                timestamps = data_content["timestamp"]
                                alpha_values = data_content["alpha"]
                                
                                if timestamps and alpha_values and len(timestamps) == len(alpha_values):
                                    df = pd.DataFrame({
                                        'timestamp': [datetime.fromtimestamp(ts) for ts in timestamps],
                                        f'{factor_name}_alpha': alpha_values
                                    })
                                    df.set_index('timestamp', inplace=True)
                                    df = df.sort_index()
                                    
                                    self.logger.info(f"✅ {factor_name} 數據獲取成功: {len(df)}條記錄")
                                    return df
                                else:
                                    self.logger.error(f"❌ {factor_name} 數據長度不匹配")
                            else:
                                self.logger.error(f"❌ {factor_name} 響應缺少必要字段")
                        else:
                            self.logger.error(f"❌ {factor_name} 響應格式錯誤")
                            
                    except Exception as json_error:
                        self.logger.error(f"❌ {factor_name} JSON解析失敗: {json_error}")
                        self.logger.error(f"原始響應: {response_text[:500]}")
                else:
                    self.logger.error(f"❌ {factor_name} API響應異常: {response.status}")
                    self.logger.error(f"響應內容: {response_text[:500]}")
                
                return None
                
        except Exception as e:
            self.logger.error(f"❌ {factor_name} 數據獲取失敗: {e}")
            return None
    
    async def download_all_factors(self, symbol: str = "BTCUSDT", 
                                  start_date: str = "2023-12-31", 
                                  end_date: str = "2025-06-30") -> Dict[str, pd.DataFrame]:
        """下載所有因子數據"""
        self.logger.info(f"🚀 開始下載所有因子數據: {symbol}")
        self.logger.info(f"📅 時間範圍: {start_date} 到 {end_date}")
        
        all_factor_data = {}
        
        for factor_name, description in self.factors.items():
            self.logger.info(f"📥 正在下載: {description}")
            
            factor_data = await self.get_factor_data(
                factor_name=factor_name,
                symbol=symbol,
                period="15min",
                start_date=start_date,
                end_date=end_date
            )
            
            if factor_data is not None:
                all_factor_data[factor_name] = factor_data
                
                # 保存到文件
                filename = f"{self.data_dir}/{factor_name}_{symbol}_15min.csv"
                factor_data.to_csv(filename)
                self.logger.info(f"💾 {factor_name} 數據已保存到: {filename}")
            else:
                self.logger.warning(f"⚠️ {factor_name} 數據獲取失敗")
            
            # 避免請求過於頻繁
            await asyncio.sleep(1)
        
        self.logger.info(f"✅ 因子數據下載完成，成功獲取 {len(all_factor_data)}/{len(self.factors)} 個因子")
        return all_factor_data
    
    def save_summary(self, all_factor_data: Dict[str, pd.DataFrame], symbol: str = "BTCUSDT"):
        """保存數據摘要"""
        summary = {
            "symbol": symbol,
            "download_time": datetime.now().isoformat(),
            "factors": {}
        }
        
        for factor_name, data in all_factor_data.items():
            summary["factors"][factor_name] = {
                "description": self.factors[factor_name],
                "records_count": len(data),
                "date_range": {
                    "start": data.index.min().isoformat(),
                    "end": data.index.max().isoformat()
                },
                "columns": list(data.columns)
            }
        
        summary_file = f"{self.data_dir}/download_summary_{symbol}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"📋 數據摘要已保存到: {summary_file}")
        return summary

async def main():
    """主函數 - 下載所有 Blave 因子數據"""
    async with BlaveFactorDownloader() as downloader:
        # 下載 BTCUSDT 的所有因子數據
        all_data = await downloader.download_all_factors(
            symbol="BTCUSDT",
            start_date="2023-12-31",
            end_date="2025-06-30"
        )
        
        # 保存摘要
        summary = downloader.save_summary(all_data, "BTCUSDT")
        
        print("\n" + "="*60)
        print("📊 Blave 因子數據下載完成!")
        print("="*60)
        print(f"成功下載因子數量: {len(all_data)}")
        for factor_name, description in downloader.factors.items():
            status = "✅" if factor_name in all_data else "❌"
            print(f"{status} {description}")
        print("="*60)

if __name__ == "__main__":
    asyncio.run(main())
