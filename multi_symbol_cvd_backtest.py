"""
多幣種 CVD 策略回測系統
對多個加密貨幣執行相同的 CVD 背離策略回測，驗證策略的普適性

測試幣種: ETH, SOL, BNB, TRB, 1000PEPE, DOGE, XRP

作者: 專業量化策略工程師
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from cvd_trading_strategy import CVDTradingStrategy
import os
from typing import Dict, List
import warnings
warnings.filterwarnings('ignore')

class MultiSymbolCVDBacktest:
    """多幣種 CVD 回測系統"""
    
    def __init__(self):
        self.symbols = [
            "ETHUSDT",
            "SOLUSDT", 
            "BNBUSDT",
            "TRBUSDT",
            "1000PEPEUSDT",
            "DOGEUSDT",
            "XRPUSDT"
        ]
        
        self.data_dir = "multi_symbol_data"
        self.results_dir = "multi_symbol_results"
        os.makedirs(self.results_dir, exist_ok=True)
        
        # 統一策略參數 - 調整盈虧比為 2:1
        self.strategy_params = {
            'initial_capital': 10000,
            'risk_per_trade': 0.01,
            'leverage': 10.0,
            'take_profit_atr': 2.0,  # 調整為 2.0 ATR
            'stop_loss_atr': 1.0
        }
        
        self.all_results = {}
    
    def load_symbol_data(self, symbol: str) -> pd.DataFrame:
        """載入單個幣種數據"""
        try:
            filename = f"{self.data_dir}/{symbol}_15m_data.csv"
            df = pd.read_csv(filename, index_col=0, parse_dates=True)
            print(f"✅ {symbol} 數據載入成功: {len(df)} 條記錄")
            return df
        except Exception as e:
            print(f"❌ {symbol} 數據載入失敗: {e}")
            return pd.DataFrame()
    
    def run_single_symbol_backtest(self, symbol: str) -> Dict:
        """運行單個幣種的回測"""
        print(f"\n🚀 開始 {symbol} CVD 策略回測...")
        print("-" * 50)
        
        # 載入數據
        df = self.load_symbol_data(symbol)
        if df.empty:
            return {}
        
        # 創建策略實例
        strategy = CVDTradingStrategy(**self.strategy_params)
        
        # 運行回測
        try:
            performance = strategy.run_backtest(df)
            
            if performance:
                # 保存詳細結果
                strategy.save_results()
                
                # 重命名文件以區分不同幣種
                if os.path.exists('cvd_strategy_trades.csv'):
                    os.rename('cvd_strategy_trades.csv', f'{self.results_dir}/{symbol}_trades.csv')
                if os.path.exists('cvd_strategy_equity.csv'):
                    os.rename('cvd_strategy_equity.csv', f'{self.results_dir}/{symbol}_equity.csv')
                if os.path.exists('cvd_strategy_results.png'):
                    os.rename('cvd_strategy_results.png', f'{self.results_dir}/{symbol}_results.png')
                
                # 添加幣種信息
                performance['symbol'] = symbol
                performance['data_points'] = len(df)
                performance['date_range'] = {
                    'start': str(df.index.min()),
                    'end': str(df.index.max())
                }
                
                print(f"✅ {symbol} 回測完成")
                return performance
            else:
                print(f"❌ {symbol} 回測失敗")
                return {}
                
        except Exception as e:
            print(f"❌ {symbol} 回測出錯: {e}")
            return {}
    
    def run_all_symbols_backtest(self) -> Dict[str, Dict]:
        """運行所有幣種的回測"""
        print("🚀 多幣種 CVD 策略回測系統")
        print("=" * 80)
        print(f"測試幣種: {', '.join(self.symbols)}")
        print(f"策略參數: {self.strategy_params}")
        print("=" * 80)
        
        all_results = {}
        
        for symbol in self.symbols:
            result = self.run_single_symbol_backtest(symbol)
            if result:
                all_results[symbol] = result
        
        self.all_results = all_results
        return all_results
    
    def generate_comparison_report(self) -> str:
        """生成對比報告"""
        if not self.all_results:
            return "沒有回測結果可供分析"
        
        print("\n📊 生成多幣種對比報告...")
        
        # 創建對比表格
        comparison_data = []
        
        for symbol, result in self.all_results.items():
            comparison_data.append({
                'Symbol': symbol.replace('USDT', ''),
                'Total_Return_%': result.get('total_return', 0),
                'Win_Rate_%': result.get('win_rate', 0) * 100,
                'Total_Trades': result.get('total_trades', 0),
                'Max_Drawdown_%': result.get('max_drawdown', 0),
                'Sharpe_Ratio': result.get('sharpe_ratio', 0),
                'Profit_Factor': result.get('profit_factor', 0),
                'Avg_Win_$': result.get('avg_win', 0),
                'Avg_Loss_$': result.get('avg_loss', 0)
            })
        
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df = comparison_df.sort_values('Total_Return_%', ascending=False)
        
        # 保存對比表格
        comparison_df.to_csv(f'{self.results_dir}/strategy_comparison.csv', index=False)
        
        # 生成報告
        report = []
        report.append("=" * 100)
        report.append("📊 多幣種 CVD 策略回測對比報告")
        report.append("=" * 100)
        report.append(f"回測時間: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"測試幣種: {len(self.all_results)} 個")
        report.append(f"策略參數: 初始資金 ${self.strategy_params['initial_capital']:,}, "
                     f"風險 {self.strategy_params['risk_per_trade']*100}%, "
                     f"槓桿 {self.strategy_params['leverage']}x")
        report.append("")
        
        # 詳細結果表格
        report.append("📈 詳細回測結果:")
        report.append("")
        
        # 表頭
        header = f"{'幣種':<8} {'收益率':<8} {'勝率':<8} {'交易數':<8} {'回撤':<8} {'Sharpe':<8} {'盈虧比':<8}"
        report.append(header)
        report.append("-" * len(header))
        
        # 數據行
        for _, row in comparison_df.iterrows():
            line = f"{row['Symbol']:<8} {row['Total_Return_%']:<7.1f}% {row['Win_Rate_%']:<7.1f}% {row['Total_Trades']:<8} {row['Max_Drawdown_%']:<7.1f}% {row['Sharpe_Ratio']:<7.1f} {row['Profit_Factor']:<7.1f}"
            report.append(line)
        
        report.append("")
        
        # 統計分析
        report.append("📊 統計分析:")
        
        returns = comparison_df['Total_Return_%'].values
        win_rates = comparison_df['Win_Rate_%'].values
        sharpe_ratios = comparison_df['Sharpe_Ratio'].values
        
        report.append(f"  收益率統計:")
        report.append(f"    平均: {np.mean(returns):.1f}%")
        report.append(f"    中位數: {np.median(returns):.1f}%")
        report.append(f"    標準差: {np.std(returns):.1f}%")
        report.append(f"    範圍: {np.min(returns):.1f}% ~ {np.max(returns):.1f}%")
        
        report.append(f"  勝率統計:")
        report.append(f"    平均: {np.mean(win_rates):.1f}%")
        report.append(f"    中位數: {np.median(win_rates):.1f}%")
        report.append(f"    範圍: {np.min(win_rates):.1f}% ~ {np.max(win_rates):.1f}%")
        
        report.append(f"  Sharpe比率統計:")
        report.append(f"    平均: {np.mean(sharpe_ratios):.2f}")
        report.append(f"    中位數: {np.median(sharpe_ratios):.2f}")
        report.append(f"    範圍: {np.min(sharpe_ratios):.2f} ~ {np.max(sharpe_ratios):.2f}")
        
        # 策略評估
        report.append("")
        report.append("🎯 策略評估:")
        
        positive_returns = len([r for r in returns if r > 0])
        high_win_rate = len([w for w in win_rates if w > 60])
        good_sharpe = len([s for s in sharpe_ratios if s > 1])
        
        report.append(f"  正收益幣種: {positive_returns}/{len(returns)} ({positive_returns/len(returns)*100:.1f}%)")
        report.append(f"  高勝率幣種 (>60%): {high_win_rate}/{len(win_rates)} ({high_win_rate/len(win_rates)*100:.1f}%)")
        report.append(f"  良好Sharpe幣種 (>1): {good_sharpe}/{len(sharpe_ratios)} ({good_sharpe/len(sharpe_ratios)*100:.1f}%)")
        
        # 結論
        report.append("")
        report.append("💡 結論:")
        
        if positive_returns >= len(returns) * 0.7:
            report.append("  ✅ 策略在大多數幣種上表現良好，具有較強的普適性")
        elif positive_returns >= len(returns) * 0.5:
            report.append("  ⚠️ 策略表現中等，在部分幣種上有效")
        else:
            report.append("  ❌ 策略普適性較差，可能存在過擬合問題")
        
        if np.mean(win_rates) > 60:
            report.append("  ✅ 整體勝率較高，信號質量良好")
        else:
            report.append("  ⚠️ 整體勝率一般，需要進一步優化")
        
        if np.std(returns) > 20:
            report.append("  ⚠️ 不同幣種間表現差異較大，策略穩定性有待提升")
        else:
            report.append("  ✅ 不同幣種間表現相對穩定")
        
        report.append("")
        report.append("=" * 100)
        
        # 保存報告
        report_text = "\n".join(report)
        with open(f"{self.results_dir}/multi_symbol_comparison_report.txt", "w", encoding="utf-8") as f:
            f.write(report_text)
        
        print("✅ 對比報告已保存")
        print(report_text)
        
        return report_text
    
    def plot_comparison_charts(self):
        """繪製對比圖表"""
        if not self.all_results:
            return
        
        print("\n📊 生成對比圖表...")
        
        # 準備數據
        symbols = []
        returns = []
        win_rates = []
        sharpe_ratios = []
        max_drawdowns = []
        
        for symbol, result in self.all_results.items():
            symbols.append(symbol.replace('USDT', ''))
            returns.append(result.get('total_return', 0))
            win_rates.append(result.get('win_rate', 0) * 100)
            sharpe_ratios.append(result.get('sharpe_ratio', 0))
            max_drawdowns.append(abs(result.get('max_drawdown', 0)))
        
        # 創建圖表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 收益率對比
        bars1 = ax1.bar(symbols, returns, color=['green' if r > 0 else 'red' for r in returns])
        ax1.set_title('Total Return by Symbol', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Return (%)')
        ax1.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax1.tick_params(axis='x', rotation=45)
        
        # 添加數值標籤
        for bar, value in zip(bars1, returns):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + (1 if height > 0 else -3),
                    f'{value:.1f}%', ha='center', va='bottom' if height > 0 else 'top')
        
        # 勝率對比
        bars2 = ax2.bar(symbols, win_rates, color='blue', alpha=0.7)
        ax2.set_title('Win Rate by Symbol', fontsize=14, fontweight='bold')
        ax2.set_ylabel('Win Rate (%)')
        ax2.axhline(y=50, color='red', linestyle='--', alpha=0.5, label='50% Baseline')
        ax2.tick_params(axis='x', rotation=45)
        ax2.legend()
        
        # 添加數值標籤
        for bar, value in zip(bars2, win_rates):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{value:.1f}%', ha='center', va='bottom')
        
        # Sharpe比率對比
        bars3 = ax3.bar(symbols, sharpe_ratios, color='purple', alpha=0.7)
        ax3.set_title('Sharpe Ratio by Symbol', fontsize=14, fontweight='bold')
        ax3.set_ylabel('Sharpe Ratio')
        ax3.axhline(y=1, color='red', linestyle='--', alpha=0.5, label='1.0 Baseline')
        ax3.tick_params(axis='x', rotation=45)
        ax3.legend()
        
        # 添加數值標籤
        for bar, value in zip(bars3, sharpe_ratios):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{value:.1f}', ha='center', va='bottom')
        
        # 最大回撤對比
        bars4 = ax4.bar(symbols, max_drawdowns, color='orange', alpha=0.7)
        ax4.set_title('Maximum Drawdown by Symbol', fontsize=14, fontweight='bold')
        ax4.set_ylabel('Max Drawdown (%)')
        ax4.tick_params(axis='x', rotation=45)
        
        # 添加數值標籤
        for bar, value in zip(bars4, max_drawdowns):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{value:.1f}%', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig(f'{self.results_dir}/multi_symbol_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 對比圖表已保存")

def main():
    """主函數"""
    # 創建多幣種回測系統
    backtest_system = MultiSymbolCVDBacktest()
    
    # 運行所有幣種回測
    results = backtest_system.run_all_symbols_backtest()
    
    if results:
        # 生成對比報告
        backtest_system.generate_comparison_report()
        
        # 繪製對比圖表
        backtest_system.plot_comparison_charts()
        
        print(f"\n🎉 多幣種回測完成!")
        print(f"📁 結果保存在: {backtest_system.results_dir}/")
        print(f"📊 成功測試 {len(results)} 個幣種")
    else:
        print("❌ 沒有成功的回測結果")

if __name__ == "__main__":
    main()
