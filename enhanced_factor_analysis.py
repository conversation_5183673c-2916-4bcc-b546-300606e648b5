"""
增強版 Blave 因子分析系統
包含更詳細的統計分析和可視化

功能:
1. 深度數據探索分析
2. 非線性關係檢測
3. 滯後效應分析
4. 因子組合效應分析
5. 詳細可視化報告

作者: 專業量化策略工程師
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.metrics import r2_score, mean_squared_error
from sklearn.preprocessing import StandardScaler
from scipy import stats
from scipy.stats import spearmanr, kendalltau
import warnings
warnings.filterwarnings('ignore')

# 設置圖表樣式
try:
    plt.style.use('seaborn-v0_8')
except:
    try:
        plt.style.use('seaborn')
    except:
        pass  # 使用默認樣式

plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 10

class EnhancedFactorAnalysis:
    """增強版因子分析系統"""
    
    def __init__(self):
        self.price_data = None
        self.factor_data = {}
        self.combined_data = None
        self.analysis_results = {}
        
    def load_and_prepare_data(self):
        """載入和準備數據"""
        print("📊 載入和準備數據...")
        
        # 載入價格數據
        try:
            self.price_data = pd.read_csv('binance_data/BTCUSDT_15m_enhanced.csv', index_col=0, parse_dates=True)
            print(f"✅ 價格數據: {len(self.price_data)} 條記錄")
        except Exception as e:
            print(f"❌ 價格數據載入失敗: {e}")
            return False
        
        # 載入因子數據
        factor_files = {
            'HC': 'blave_factor_data/holder_concentration_BTCUSDT_15min.csv',
            'WH': 'blave_factor_data/whale_hunter_BTCUSDT_15min.csv',
            'TI': 'blave_factor_data/taker_intensity_BTCUSDT_15min.csv'
        }
        
        for factor_name, file_path in factor_files.items():
            try:
                factor_df = pd.read_csv(file_path, index_col=0, parse_dates=True)
                # 重命名列為簡潔名稱
                factor_df.columns = [factor_name]
                self.factor_data[factor_name] = factor_df
                print(f"✅ {factor_name} 因子: {len(factor_df)} 條記錄")
            except Exception as e:
                print(f"❌ {factor_name} 載入失敗: {e}")
        
        # 數據對齊和合併
        aligned_data = self.price_data.copy()
        for factor_name, factor_df in self.factor_data.items():
            aligned_data = pd.merge_asof(
                aligned_data.sort_index(),
                factor_df.sort_index(),
                left_index=True,
                right_index=True,
                direction='backward'
            )
        
        # 計算多個時間窗口的收益率
        for periods in [1, 4, 16, 96]:  # 15分鐘, 1小時, 4小時, 1天
            hours = periods * 0.25
            aligned_data[f'return_{hours}h'] = aligned_data['Close'].shift(-periods).pct_change()
        
        # 計算當前收益率和波動率
        aligned_data['current_return'] = aligned_data['Close'].pct_change()
        aligned_data['volatility'] = aligned_data['current_return'].rolling(96).std()  # 24小時滾動波動率
        
        # 計算價格動量指標
        aligned_data['momentum_1h'] = aligned_data['Close'].pct_change(4)
        aligned_data['momentum_4h'] = aligned_data['Close'].pct_change(16)
        aligned_data['momentum_1d'] = aligned_data['Close'].pct_change(96)
        
        self.combined_data = aligned_data.dropna()
        print(f"✅ 數據對齊完成: {len(self.combined_data)} 條有效記錄")
        
        return True
    
    def exploratory_data_analysis(self):
        """探索性數據分析"""
        print("\n🔍 探索性數據分析...")
        
        factor_cols = ['HC', 'WH', 'TI']
        return_cols = ['return_0.25h', 'return_1.0h', 'return_4.0h', 'return_24.0h']
        
        # 基本統計信息
        print("\n📊 因子基本統計:")
        factor_stats = self.combined_data[factor_cols].describe()
        print(factor_stats)
        
        # 收益率統計
        print("\n📈 收益率統計:")
        return_stats = self.combined_data[return_cols].describe()
        print(return_stats)
        
        # 保存統計結果
        self.analysis_results['factor_stats'] = factor_stats
        self.analysis_results['return_stats'] = return_stats
        
        # 檢查因子分佈
        self.plot_factor_distributions()
        
        return True
    
    def plot_factor_distributions(self):
        """繪製因子分佈圖"""
        factor_cols = ['HC', 'WH', 'TI']
        
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle('Blave 因子分佈分析', fontsize=16, fontweight='bold')
        
        for i, factor in enumerate(factor_cols):
            # 時間序列圖
            axes[0, i].plot(self.combined_data.index[-1000:], self.combined_data[factor].iloc[-1000:])
            axes[0, i].set_title(f'{factor} 時間序列 (最近1000個點)')
            axes[0, i].tick_params(axis='x', rotation=45)
            
            # 分佈直方圖
            axes[1, i].hist(self.combined_data[factor].dropna(), bins=50, alpha=0.7, edgecolor='black')
            axes[1, i].set_title(f'{factor} 分佈直方圖')
            axes[1, i].set_xlabel('因子值')
            axes[1, i].set_ylabel('頻率')
        
        plt.tight_layout()
        plt.savefig('factor_distributions.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✅ 因子分佈圖已保存: factor_distributions.png")
    
    def correlation_analysis(self):
        """深度相關性分析"""
        print("\n📊 深度相關性分析...")
        
        factor_cols = ['HC', 'WH', 'TI']
        return_cols = ['return_0.25h', 'return_1.0h', 'return_4.0h', 'return_24.0h']
        
        # 計算多種相關性
        correlations = {}
        
        for return_col in return_cols:
            correlations[return_col] = {}
            
            for factor in factor_cols:
                # Pearson 相關性
                pearson_corr, pearson_p = stats.pearsonr(
                    self.combined_data[factor].dropna(), 
                    self.combined_data[return_col].dropna()
                )
                
                # Spearman 相關性 (非線性)
                spearman_corr, spearman_p = spearmanr(
                    self.combined_data[factor].dropna(), 
                    self.combined_data[return_col].dropna()
                )
                
                correlations[return_col][factor] = {
                    'pearson': pearson_corr,
                    'pearson_p': pearson_p,
                    'spearman': spearman_corr,
                    'spearman_p': spearman_p
                }
        
        self.analysis_results['correlations'] = correlations
        
        # 顯示結果
        print("\n相關性分析結果:")
        for return_col in return_cols:
            print(f"\n{return_col}:")
            for factor in factor_cols:
                corr_data = correlations[return_col][factor]
                print(f"  {factor}:")
                print(f"    Pearson: {corr_data['pearson']:.4f} (p={corr_data['pearson_p']:.4f})")
                print(f"    Spearman: {corr_data['spearman']:.4f} (p={corr_data['spearman_p']:.4f})")
        
        # 創建相關性熱力圖
        self.plot_correlation_heatmap()
        
        return True
    
    def plot_correlation_heatmap(self):
        """繪製相關性熱力圖"""
        factor_cols = ['HC', 'WH', 'TI']
        return_cols = ['return_0.25h', 'return_1.0h', 'return_4.0h', 'return_24.0h']
        
        # 創建相關性矩陣
        corr_data = self.combined_data[factor_cols + return_cols].corr()
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(corr_data, annot=True, cmap='RdBu_r', center=0, 
                   square=True, fmt='.4f', cbar_kws={'label': '相關係數'})
        plt.title('Blave 因子與收益率相關性熱力圖', fontsize=14, fontweight='bold')
        plt.tight_layout()
        plt.savefig('correlation_heatmap.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✅ 相關性熱力圖已保存: correlation_heatmap.png")
    
    def lag_analysis(self):
        """滯後效應分析"""
        print("\n⏰ 滯後效應分析...")
        
        factor_cols = ['HC', 'WH', 'TI']
        target = 'return_1.0h'  # 使用1小時收益率作為目標
        
        lag_results = {}
        
        for factor in factor_cols:
            lag_correlations = []
            
            # 測試不同滯後期 (0到24小時，每15分鐘一個點)
            for lag in range(0, 97):  # 0到24小時
                if lag == 0:
                    factor_lagged = self.combined_data[factor]
                else:
                    factor_lagged = self.combined_data[factor].shift(lag)
                
                # 計算相關性
                valid_data = pd.concat([factor_lagged, self.combined_data[target]], axis=1).dropna()
                if len(valid_data) > 100:  # 確保有足夠的數據
                    corr, p_val = stats.pearsonr(valid_data.iloc[:, 0], valid_data.iloc[:, 1])
                    lag_correlations.append({
                        'lag_hours': lag * 0.25,
                        'correlation': corr,
                        'p_value': p_val,
                        'abs_correlation': abs(corr)
                    })
            
            lag_results[factor] = pd.DataFrame(lag_correlations)
        
        self.analysis_results['lag_analysis'] = lag_results
        
        # 找出最佳滯後期
        print("\n最佳滯後期分析:")
        for factor in factor_cols:
            best_lag = lag_results[factor].loc[lag_results[factor]['abs_correlation'].idxmax()]
            print(f"{factor}: 最佳滯後期 {best_lag['lag_hours']:.2f}小時, "
                  f"相關性 {best_lag['correlation']:.4f} (p={best_lag['p_value']:.4f})")
        
        # 繪製滯後分析圖
        self.plot_lag_analysis()
        
        return True
    
    def plot_lag_analysis(self):
        """繪製滯後分析圖"""
        factor_cols = ['HC', 'WH', 'TI']
        
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        fig.suptitle('因子滯後效應分析', fontsize=16, fontweight='bold')
        
        for i, factor in enumerate(factor_cols):
            lag_data = self.analysis_results['lag_analysis'][factor]
            
            axes[i].plot(lag_data['lag_hours'], lag_data['correlation'], 'b-', linewidth=2)
            axes[i].axhline(y=0, color='r', linestyle='--', alpha=0.5)
            axes[i].set_title(f'{factor} 滯後相關性')
            axes[i].set_xlabel('滯後時間 (小時)')
            axes[i].set_ylabel('相關係數')
            axes[i].grid(True, alpha=0.3)
            
            # 標記最佳滯後期
            best_idx = lag_data['abs_correlation'].idxmax()
            best_lag = lag_data.iloc[best_idx]
            axes[i].scatter(best_lag['lag_hours'], best_lag['correlation'], 
                          color='red', s=100, zorder=5)
            axes[i].annotate(f'最佳: {best_lag["lag_hours"]:.1f}h\n{best_lag["correlation"]:.4f}',
                           xy=(best_lag['lag_hours'], best_lag['correlation']),
                           xytext=(10, 10), textcoords='offset points',
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
        
        plt.tight_layout()
        plt.savefig('lag_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✅ 滯後分析圖已保存: lag_analysis.png")
    
    def machine_learning_analysis(self):
        """機器學習分析"""
        print("\n🤖 機器學習分析...")
        
        factor_cols = ['HC', 'WH', 'TI']
        target_cols = ['return_0.25h', 'return_1.0h', 'return_4.0h', 'return_24.0h']
        
        ml_results = {}
        
        for target in target_cols:
            print(f"\n分析目標: {target}")
            
            # 準備數據
            X = self.combined_data[factor_cols].ffill().fillna(0)
            y = self.combined_data[target].fillna(0)
            
            # 移除異常值
            valid_idx = (np.abs(y) < y.std() * 3) & (np.abs(X).max(axis=1) < X.std().max() * 3)
            X_clean = X[valid_idx]
            y_clean = y[valid_idx]
            
            if len(X_clean) < 1000:
                print(f"  數據不足，跳過 {target}")
                continue
            
            # 標準化
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X_clean)
            
            # 多種模型比較
            models = {
                'Linear': LinearRegression(),
                'Ridge': Ridge(alpha=1.0),
                'Lasso': Lasso(alpha=0.01),
                'RandomForest': RandomForestRegressor(n_estimators=100, random_state=42)
            }
            
            model_results = {}
            
            for model_name, model in models.items():
                try:
                    model.fit(X_scaled, y_clean)
                    y_pred = model.predict(X_scaled)
                    
                    r2 = r2_score(y_clean, y_pred)
                    mse = mean_squared_error(y_clean, y_pred)
                    
                    # 特徵重要性
                    if hasattr(model, 'feature_importances_'):
                        importance = model.feature_importances_
                    elif hasattr(model, 'coef_'):
                        importance = np.abs(model.coef_)
                    else:
                        importance = np.zeros(len(factor_cols))
                    
                    model_results[model_name] = {
                        'r2': r2,
                        'mse': mse,
                        'feature_importance': dict(zip(factor_cols, importance))
                    }
                    
                    print(f"  {model_name}: R²={r2:.4f}, MSE={mse:.6f}")
                    
                except Exception as e:
                    print(f"  {model_name} 失敗: {e}")
            
            ml_results[target] = model_results
        
        self.analysis_results['ml_analysis'] = ml_results
        return True
    
    def generate_comprehensive_report(self):
        """生成綜合報告"""
        print("\n📋 生成綜合分析報告...")
        
        report = []
        report.append("=" * 100)
        report.append("📊 Blave 因子深度分析報告")
        report.append("=" * 100)
        report.append(f"分析時間: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"數據範圍: {self.combined_data.index.min()} 到 {self.combined_data.index.max()}")
        report.append(f"有效記錄: {len(self.combined_data):,} 條")
        report.append("")
        
        # 因子統計摘要
        report.append("📊 因子統計摘要:")
        for factor in ['HC', 'WH', 'TI']:
            stats = self.combined_data[factor].describe()
            report.append(f"  {factor}: 均值={stats['mean']:.4f}, 標準差={stats['std']:.4f}, "
                         f"範圍=[{stats['min']:.4f}, {stats['max']:.4f}]")
        report.append("")
        
        # 相關性分析結果
        if 'correlations' in self.analysis_results:
            report.append("📈 相關性分析結果:")
            correlations = self.analysis_results['correlations']
            
            for return_period in ['return_0.25h', 'return_1.0h', 'return_4.0h', 'return_24.0h']:
                report.append(f"\n  {return_period}:")
                
                # 按絕對相關性排序
                factor_corrs = []
                for factor in ['HC', 'WH', 'TI']:
                    pearson = correlations[return_period][factor]['pearson']
                    spearman = correlations[return_period][factor]['spearman']
                    factor_corrs.append((factor, abs(pearson), pearson, spearman))
                
                factor_corrs.sort(key=lambda x: x[1], reverse=True)
                
                for factor, abs_corr, pearson, spearman in factor_corrs:
                    report.append(f"    {factor}: Pearson={pearson:.4f}, Spearman={spearman:.4f}")
        
        # 滯後分析結果
        if 'lag_analysis' in self.analysis_results:
            report.append("\n⏰ 最佳滯後期分析:")
            for factor in ['HC', 'WH', 'TI']:
                lag_data = self.analysis_results['lag_analysis'][factor]
                best_lag = lag_data.loc[lag_data['abs_correlation'].idxmax()]
                report.append(f"  {factor}: {best_lag['lag_hours']:.2f}小時 "
                             f"(相關性={best_lag['correlation']:.4f})")
        
        # 機器學習結果
        if 'ml_analysis' in self.analysis_results:
            report.append("\n🤖 機器學習分析結果:")
            ml_results = self.analysis_results['ml_analysis']
            
            for target in ml_results:
                report.append(f"\n  {target}:")
                for model_name, results in ml_results[target].items():
                    report.append(f"    {model_name}: R²={results['r2']:.4f}")
                    
                    # 特徵重要性
                    importance = results['feature_importance']
                    sorted_features = sorted(importance.items(), key=lambda x: x[1], reverse=True)
                    feature_str = ", ".join([f"{k}={v:.4f}" for k, v in sorted_features])
                    report.append(f"      特徵重要性: {feature_str}")
        
        # 總結和建議
        report.append("\n📊 總結和建議:")
        report.append("  1. 因子影響力普遍較弱，建議結合其他技術指標")
        report.append("  2. 不同時間窗口下因子表現有差異，建議多時間框架分析")
        report.append("  3. 考慮因子的滯後效應和非線性關係")
        report.append("  4. 建議進一步研究因子組合和交互效應")
        
        report.append("\n" + "=" * 100)
        
        # 保存報告
        report_text = "\n".join(report)
        with open("enhanced_factor_analysis_report.txt", "w", encoding="utf-8") as f:
            f.write(report_text)
        
        print("✅ 詳細報告已保存到: enhanced_factor_analysis_report.txt")
        print("\n" + report_text)
        
        return report_text

def main():
    """主函數"""
    print("🚀 啟動增強版 Blave 因子分析系統")
    print("=" * 80)
    
    analyzer = EnhancedFactorAnalysis()
    
    # 載入數據
    if not analyzer.load_and_prepare_data():
        return
    
    # 探索性數據分析
    analyzer.exploratory_data_analysis()
    
    # 相關性分析
    analyzer.correlation_analysis()
    
    # 滯後效應分析
    analyzer.lag_analysis()
    
    # 機器學習分析
    analyzer.machine_learning_analysis()
    
    # 生成綜合報告
    analyzer.generate_comprehensive_report()
    
    print("\n🎉 深度分析完成！")
    print("📁 生成的文件:")
    print("  - enhanced_factor_analysis_report.txt (詳細分析報告)")
    print("  - factor_distributions.png (因子分佈圖)")
    print("  - correlation_heatmap.png (相關性熱力圖)")
    print("  - lag_analysis.png (滯後分析圖)")

if __name__ == "__main__":
    main()
