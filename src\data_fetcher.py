"""
數據獲取模組
從Blave API和Bybit API獲取實時數據

作者: 專業量化策略工程師
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from .logger_setup import setup_logger

class DataFetcher:
    """數據獲取器"""
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger("DataFetcher")
        
        # API配置
        self.blave_base_url = config.get('data.blave_base_url')
        self.bybit_base_url = config.get('data.bybit_base_url')
        
        # API密鑰
        self.blave_headers = {
            "api-key": config.get_api_key('blave', 'api_key'),
            "secret-key": config.get_api_key('blave', 'secret_key')
        }
        
        # 會話
        self.session = None
    
    async def __aenter__(self):
        """異步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """異步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def get_latest_data(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """獲取最新的完整數據"""
        try:
            self.logger.info(f"📊 獲取 {symbol} {timeframe} 最新數據")
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            # 並行獲取所需數據 (RSI+Bollinger+TI策略只需要價格和TI數據)
            tasks = [
                self.get_bybit_data(symbol, timeframe),
                self.get_blave_taker_intensity_data(symbol, timeframe)
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)
            price_data = results[0] if not isinstance(results[0], Exception) else None
            intensity_data = results[1] if not isinstance(results[1], Exception) else None

            # 檢查RSI+Bollinger+TI策略所需數據
            self.logger.info(f"📊 {symbol} 數據獲取結果:")
            self.logger.info(f"  - 價格數據: {'✅' if price_data is not None else '❌'}")
            self.logger.info(f"  - Taker Intensity: {'✅' if intensity_data is not None else '❌'}")

            # 嚴格要求價格和TI數據都必須獲取成功
            if not all([price_data is not None, intensity_data is not None]):
                self.logger.error(f"❌ {symbol} 數據獲取失敗 - 缺少價格或TI數據")
                return None
            
            # 合併數據 (只需要價格和TI數據)
            combined_data = self.merge_data(price_data, intensity_data)
            
            if combined_data is not None and len(combined_data) > 0:
                self.logger.info(f"✅ {symbol} 數據獲取成功: {len(combined_data)}條記錄")
                return combined_data
            else:
                self.logger.warning(f"⚠️ {symbol} 數據合併失敗")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ {symbol} 數據獲取失敗: {e}")
            return None




    
    async def get_bybit_data(self, symbol: str, timeframe: str, start_date: str = None, end_date: str = None) -> Optional[pd.DataFrame]:
        """獲取Bybit價格數據 - 支持歷史數據範圍查詢"""
        try:
            # 時框映射 - 根據Bybit API v5文檔
            interval_map = {
                '15min': '15',
                '1H': '60',
                '4H': '240',
                'Daily': 'D'
            }

            interval = interval_map.get(timeframe, '15')

            url = f"{self.bybit_base_url}/v5/market/kline"
            params = {
                "category": "linear",
                "symbol": symbol,
                "interval": interval,
                "limit": 1000  # 最大限制
            }

            # 如果指定了時間範圍，添加時間參數
            if start_date:
                start_timestamp = int(pd.to_datetime(start_date).timestamp() * 1000)
                params["start"] = start_timestamp
            if end_date:
                end_timestamp = int(pd.to_datetime(end_date).timestamp() * 1000)
                params["end"] = end_timestamp

            all_data = []

            # 如果需要獲取大量歷史數據，需要分批獲取
            if start_date and end_date:
                current_end = pd.to_datetime(end_date)
                start_dt = pd.to_datetime(start_date)

                while current_end > start_dt:
                    params["end"] = int(current_end.timestamp() * 1000)

                    async with self.session.get(url, params=params, timeout=30) as response:
                        if response.status == 200:
                            data = await response.json()

                            if data.get("retCode") == 0:
                                kline_data = data.get("result", {}).get("list", [])

                                if kline_data:
                                    all_data.extend(kline_data)
                                    # 更新下一批的結束時間
                                    last_timestamp = int(kline_data[-1][0])
                                    current_end = pd.to_datetime(last_timestamp, unit='ms') - pd.Timedelta(minutes=1)
                                else:
                                    break
                            else:
                                self.logger.error(f"❌ Bybit API錯誤: {data.get('retMsg')}")
                                break
                        else:
                            self.logger.warning(f"⚠️ Bybit API響應異常: {response.status}")
                            break

                    # 避免請求過於頻繁
                    await asyncio.sleep(0.1)
            else:
                # 單次請求
                async with self.session.get(url, params=params, timeout=30) as response:
                    if response.status == 200:
                        data = await response.json()

                        if data.get("retCode") == 0:
                            kline_data = data.get("result", {}).get("list", [])
                            if kline_data:
                                all_data = kline_data

            if all_data:
                df = pd.DataFrame(all_data)
                df.columns = ["timestamp", "open", "high", "low", "close", "volume", "turnover"]

                # 轉換數據類型
                for col in ["open", "high", "low", "close", "volume"]:
                    df[col] = pd.to_numeric(df[col])

                df["timestamp"] = pd.to_datetime(df["timestamp"].astype(float), unit="ms")
                df = df.sort_values("timestamp").reset_index(drop=True)
                df.set_index("timestamp", inplace=True)

                # 重命名列以匹配系統期望的格式
                df = df.rename(columns={
                    'open': 'Open',
                    'high': 'High',
                    'low': 'Low',
                    'close': 'Close',
                    'volume': 'Volume',
                    'turnover': 'Turnover'
                })

                # 去重並排序
                df = df[~df.index.duplicated(keep='first')].sort_index()

                self.logger.info(f"✅ Bybit數據獲取成功: {len(df)}條記錄")
                return df

            return None

        except Exception as e:
            self.logger.error(f"❌ Bybit數據獲取失敗: {e}")
            return None
    
    async def get_blave_concentration_data(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """獲取Blave籌碼集中度數據"""
        try:
            # 時框映射
            period_map = {
                '1H': '1h',
                '4H': '4h',
                'Daily': '1d'
            }

            period = period_map.get(timeframe, '1h')

            url = f"{self.blave_base_url}/holder_concentration/get_alpha"
            params = {
                "symbol": symbol,
                "period": period
            }

            self.logger.info(f"🔗 調用Blave籌碼集中度API: {url}")
            self.logger.info(f"📋 參數: {params}")

            async with self.session.get(url, headers=self.blave_headers, params=params, timeout=60) as response:
                response_text = await response.text()
                self.logger.info(f"📡 Blave籌碼集中度API響應狀態: {response.status}")

                if response.status == 200:
                    try:
                        data = await response.json()
                        self.logger.info(f"📊 Blave籌碼集中度響應數據結構: {list(data.keys()) if isinstance(data, dict) else 'Not dict'}")

                        # Blave API響應格式：數據在data字段中
                        if "data" in data and isinstance(data["data"], dict):
                            data_content = data["data"]
                            if "timestamp" in data_content and "alpha" in data_content:
                                timestamps = data_content["timestamp"]
                                alpha_values = data_content["alpha"]

                                if timestamps and alpha_values and len(timestamps) == len(alpha_values):
                                    df = pd.DataFrame({
                                        'timestamp': [datetime.fromtimestamp(ts) for ts in timestamps],
                                        'concentration': alpha_values
                                    })
                                    df.set_index('timestamp', inplace=True)
                                    self.logger.info(f"✅ Blave籌碼集中度數據獲取成功: {len(df)}條記錄")
                                    return df
                                else:
                                    self.logger.error(f"❌ Blave籌碼集中度數據格式錯誤: timestamps={len(timestamps) if timestamps else 0}, alpha={len(alpha_values) if alpha_values else 0}")
                            else:
                                self.logger.error(f"❌ Blave籌碼集中度響應缺少必要字段: {list(data_content.keys())}")
                        else:
                            self.logger.error(f"❌ Blave籌碼集中度響應格式錯誤: {data}")
                    except Exception as json_error:
                        self.logger.error(f"❌ Blave籌碼集中度JSON解析失敗: {json_error}")
                        self.logger.error(f"原始響應: {response_text[:500]}")
                else:
                    self.logger.error(f"❌ Blave籌碼集中度API響應異常: {response.status}")
                    self.logger.error(f"響應內容: {response_text[:500]}")

                return None

        except Exception as e:
            self.logger.error(f"❌ Blave籌碼集中度數據獲取失敗: {e}")
            return None
    
    async def get_blave_taker_intensity_data(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """獲取Blave多空力道數據"""
        try:
            period_map = {
                '1H': '1h',
                '4H': '4h',
                'Daily': '1d'
            }

            period = period_map.get(timeframe, '1h')

            url = f"{self.blave_base_url}/taker_intensity/get_alpha"
            params = {
                "symbol": symbol,
                "period": period
            }

            self.logger.info(f"🔗 調用Blave Taker Intensity API: {url}")
            self.logger.info(f"📋 參數: {params}")

            async with self.session.get(url, headers=self.blave_headers, params=params, timeout=60) as response:
                response_text = await response.text()
                self.logger.info(f"📡 Blave Taker Intensity API響應狀態: {response.status}")

                if response.status == 200:
                    try:
                        data = await response.json()
                        self.logger.info(f"📊 Blave Taker Intensity響應數據結構: {list(data.keys()) if isinstance(data, dict) else 'Not dict'}")

                        # Blave API響應格式：數據在data字段中
                        if "data" in data and isinstance(data["data"], dict):
                            data_content = data["data"]
                            if "timestamp" in data_content and "alpha" in data_content:
                                timestamps = data_content["timestamp"]
                                alpha_values = data_content["alpha"]

                                if timestamps and alpha_values and len(timestamps) == len(alpha_values):
                                    # 分離多空力道
                                    long_intensity = []
                                    short_intensity = []

                                    for val in alpha_values:
                                        if val > 0:
                                            long_intensity.append(val)
                                            short_intensity.append(0)
                                        else:
                                            long_intensity.append(0)
                                            short_intensity.append(abs(val))

                                    df = pd.DataFrame({
                                        'timestamp': [datetime.fromtimestamp(ts) for ts in timestamps],
                                        'taker_intensity': alpha_values,
                                        'long_taker_intensity': long_intensity,
                                        'short_taker_intensity': short_intensity
                                    })
                                    df.set_index('timestamp', inplace=True)
                                    self.logger.info(f"✅ Blave Taker Intensity數據獲取成功: {len(df)}條記錄")
                                    return df
                                else:
                                    self.logger.error(f"❌ Blave Taker Intensity數據格式錯誤: timestamps={len(timestamps) if timestamps else 0}, alpha={len(alpha_values) if alpha_values else 0}")
                            else:
                                self.logger.error(f"❌ Blave Taker Intensity響應缺少必要字段: {list(data_content.keys())}")
                        else:
                            self.logger.error(f"❌ Blave Taker Intensity響應格式錯誤: {data}")
                    except Exception as json_error:
                        self.logger.error(f"❌ Blave Taker Intensity JSON解析失敗: {json_error}")
                        self.logger.error(f"原始響應: {response_text[:500]}")
                else:
                    self.logger.error(f"❌ Blave Taker Intensity API響應異常: {response.status}")
                    self.logger.error(f"響應內容: {response_text[:500]}")

                return None

        except Exception as e:
            self.logger.error(f"❌ Blave Taker Intensity數據獲取失敗: {e}")
            return None
    
    def merge_data(self, price_data: pd.DataFrame, intensity_data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """合併價格和TI數據"""
        try:
            # 以價格數據為基準
            combined = price_data.copy()

            # 合併多空力道數據
            combined = pd.merge_asof(
                combined.sort_index(),
                intensity_data.sort_index(),
                left_index=True,
                right_index=True,
                direction='backward'
            )

            # 清理空值 (只檢查必要的列)
            combined = combined.dropna(subset=['Close', 'long_taker_intensity', 'short_taker_intensity'])
            
            return combined
            
        except Exception as e:
            self.logger.error(f"❌ 數據合併失敗: {e}")
            return None
