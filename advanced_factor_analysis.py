"""
高級因子組合和非線性關係分析系統
深度挖掘 Blave 因子的組合效應和非線性關係

功能:
1. 因子交互效應分析
2. 非線性關係檢測
3. 動態因子權重模型
4. 因子有效性評估
5. 實用交易策略生成

作者: 專業量化策略工程師
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.preprocessing import StandardScaler, PolynomialFeatures
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
import xgboost as xgb
from scipy import stats
from itertools import combinations
import warnings
warnings.filterwarnings('ignore')

# 設置圖表樣式
try:
    plt.style.use('seaborn')
except:
    pass
plt.rcParams['figure.figsize'] = (15, 10)
plt.rcParams['font.size'] = 10

class AdvancedFactorAnalysis:
    """高級因子分析系統"""
    
    def __init__(self):
        self.data = None
        self.factor_cols = ['HC', 'WH', 'TI']
        self.target_cols = ['return_0.25h', 'return_1.0h', 'return_4.0h', 'return_24.0h']
        self.results = {}
        
    def load_data(self):
        """載入數據"""
        print("📊 載入數據...")
        
        # 載入價格數據
        price_data = pd.read_csv('binance_data/BTCUSDT_15m_enhanced.csv', index_col=0, parse_dates=True)
        
        # 載入因子數據
        factor_files = {
            'HC': 'blave_factor_data/holder_concentration_BTCUSDT_15min.csv',
            'WH': 'blave_factor_data/whale_hunter_BTCUSDT_15min.csv',
            'TI': 'blave_factor_data/taker_intensity_BTCUSDT_15min.csv'
        }
        
        aligned_data = price_data.copy()
        for factor_name, file_path in factor_files.items():
            factor_df = pd.read_csv(file_path, index_col=0, parse_dates=True)
            factor_df.columns = [factor_name]
            aligned_data = pd.merge_asof(
                aligned_data.sort_index(),
                factor_df.sort_index(),
                left_index=True,
                right_index=True,
                direction='backward'
            )
        
        # 計算收益率
        for periods in [1, 4, 16, 96]:
            hours = periods * 0.25
            aligned_data[f'return_{hours}h'] = aligned_data['Close'].shift(-periods).pct_change()
        
        # 計算技術指標
        aligned_data['volatility'] = aligned_data['Close'].pct_change().rolling(96).std()
        aligned_data['momentum'] = aligned_data['Close'].pct_change(16)
        aligned_data['volume_ma'] = aligned_data['Volume'].rolling(96).mean()
        
        self.data = aligned_data.dropna()
        print(f"✅ 數據載入完成: {len(self.data)} 條記錄")
        
        return True
    
    def create_factor_combinations(self):
        """創建因子組合"""
        print("\n🔧 創建因子組合...")
        
        factor_combinations = {}
        
        # 1. 基礎因子
        for factor in self.factor_cols:
            factor_combinations[factor] = self.data[factor]
        
        # 2. 因子相乘組合
        for combo in combinations(self.factor_cols, 2):
            name = f"{combo[0]}*{combo[1]}"
            factor_combinations[name] = self.data[combo[0]] * self.data[combo[1]]
        
        # 三因子相乘
        name = f"{self.factor_cols[0]}*{self.factor_cols[1]}*{self.factor_cols[2]}"
        factor_combinations[name] = self.data[self.factor_cols[0]] * self.data[self.factor_cols[1]] * self.data[self.factor_cols[2]]
        
        # 3. 因子相加組合
        for combo in combinations(self.factor_cols, 2):
            name = f"{combo[0]}+{combo[1]}"
            factor_combinations[name] = self.data[combo[0]] + self.data[combo[1]]
        
        # 三因子相加
        name = f"{self.factor_cols[0]}+{self.factor_cols[1]}+{self.factor_cols[2]}"
        factor_combinations[name] = self.data[self.factor_cols[0]] + self.data[self.factor_cols[1]] + self.data[self.factor_cols[2]]
        
        # 4. 因子比值組合
        for combo in combinations(self.factor_cols, 2):
            name = f"{combo[0]}/{combo[1]}"
            denominator = self.data[combo[1]].replace(0, np.nan)
            factor_combinations[name] = self.data[combo[0]] / denominator
            
            name = f"{combo[1]}/{combo[0]}"
            denominator = self.data[combo[0]].replace(0, np.nan)
            factor_combinations[name] = self.data[combo[1]] / denominator
        
        # 5. 因子差值組合
        for combo in combinations(self.factor_cols, 2):
            name = f"{combo[0]}-{combo[1]}"
            factor_combinations[name] = self.data[combo[0]] - self.data[combo[1]]
            
            name = f"{combo[1]}-{combo[0]}"
            factor_combinations[name] = self.data[combo[1]] - self.data[combo[0]]
        
        # 6. 因子平方和立方
        for factor in self.factor_cols:
            factor_combinations[f"{factor}^2"] = self.data[factor] ** 2
            factor_combinations[f"{factor}^3"] = self.data[factor] ** 3
        
        # 7. 因子絕對值
        for factor in self.factor_cols:
            factor_combinations[f"abs({factor})"] = np.abs(self.data[factor])
        
        # 8. 因子標準化組合
        scaler = StandardScaler()
        for factor in self.factor_cols:
            scaled_data = scaler.fit_transform(self.data[[factor]])
            factor_combinations[f"std_{factor}"] = scaled_data.flatten()
        
        # 轉換為 DataFrame
        self.factor_combinations_df = pd.DataFrame(factor_combinations, index=self.data.index)
        self.factor_combinations_df = self.factor_combinations_df.replace([np.inf, -np.inf], np.nan).fillna(0)
        
        print(f"✅ 創建了 {len(factor_combinations)} 個因子組合")
        
        return factor_combinations
    
    def analyze_factor_combinations(self):
        """分析因子組合效果"""
        print("\n📈 分析因子組合效果...")
        
        combination_results = {}
        
        for target in self.target_cols:
            print(f"\n分析目標: {target}")
            combination_results[target] = {}
            
            y = self.data[target].fillna(0)
            
            # 分析每個因子組合
            for factor_name in self.factor_combinations_df.columns:
                try:
                    X = self.factor_combinations_df[[factor_name]].fillna(0)
                    
                    # 移除異常值
                    valid_idx = (np.abs(y) < y.std() * 3) & (np.abs(X[factor_name]) < X[factor_name].std() * 3)
                    X_clean = X[valid_idx]
                    y_clean = y[valid_idx]
                    
                    if len(X_clean) < 1000:
                        continue
                    
                    # 線性回歸
                    model = LinearRegression()
                    model.fit(X_clean, y_clean)
                    y_pred = model.predict(X_clean)
                    
                    # 計算指標
                    r2 = r2_score(y_clean, y_pred)
                    correlation = np.corrcoef(X_clean[factor_name], y_clean)[0, 1]
                    
                    # 統計顯著性
                    _, p_value = stats.pearsonr(X_clean[factor_name], y_clean)
                    
                    combination_results[target][factor_name] = {
                        'r2': r2,
                        'correlation': correlation,
                        'abs_correlation': abs(correlation),
                        'p_value': p_value,
                        'coefficient': model.coef_[0] if len(model.coef_) > 0 else 0
                    }
                    
                except Exception as e:
                    continue
            
            # 按絕對相關性排序
            sorted_factors = sorted(
                combination_results[target].items(),
                key=lambda x: x[1]['abs_correlation'],
                reverse=True
            )
            
            print(f"  前10個最佳因子組合:")
            for i, (factor_name, metrics) in enumerate(sorted_factors[:10]):
                significance = "***" if metrics['p_value'] < 0.001 else "**" if metrics['p_value'] < 0.01 else "*" if metrics['p_value'] < 0.05 else ""
                print(f"    {i+1:2d}. {factor_name:15s}: 相關性={metrics['correlation']:7.4f}, R²={metrics['r2']:7.4f}, p={metrics['p_value']:6.4f}{significance}")
        
        self.results['combinations'] = combination_results
        return combination_results
    
    def nonlinear_analysis(self):
        """非線性關係分析"""
        print("\n🤖 非線性關係分析...")
        
        nonlinear_results = {}
        
        # 選擇最佳因子組合進行非線性分析
        best_factors = []
        for target in self.target_cols:
            if target in self.results['combinations']:
                sorted_factors = sorted(
                    self.results['combinations'][target].items(),
                    key=lambda x: x[1]['abs_correlation'],
                    reverse=True
                )
                # 取前5個最佳因子
                best_factors.extend([f[0] for f in sorted_factors[:5]])
        
        # 去重並限制數量
        best_factors = list(set(best_factors))[:15]  # 最多15個因子
        
        print(f"選擇 {len(best_factors)} 個最佳因子進行非線性分析")
        
        for target in self.target_cols:
            print(f"\n分析目標: {target}")
            nonlinear_results[target] = {}
            
            y = self.data[target].fillna(0)
            X = self.factor_combinations_df[best_factors].fillna(0)
            
            # 移除異常值
            valid_idx = (np.abs(y) < y.std() * 3) & (np.abs(X).max(axis=1) < X.std().max() * 3)
            X_clean = X[valid_idx]
            y_clean = y[valid_idx]
            
            if len(X_clean) < 1000:
                continue
            
            # 標準化
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X_clean)
            
            # 多種非線性模型
            models = {
                'Linear': LinearRegression(),
                'Polynomial_2': None,  # 將在下面處理
                'RandomForest': RandomForestRegressor(n_estimators=100, max_depth=10, random_state=42),
                'GradientBoosting': GradientBoostingRegressor(n_estimators=100, max_depth=6, random_state=42),
                'XGBoost': xgb.XGBRegressor(n_estimators=100, max_depth=6, random_state=42, verbosity=0),
                'NeuralNetwork': MLPRegressor(hidden_layer_sizes=(50, 25), max_iter=500, random_state=42)
            }
            
            for model_name, model in models.items():
                try:
                    if model_name == 'Polynomial_2':
                        # 多項式特徵
                        poly = PolynomialFeatures(degree=2, include_bias=False)
                        X_poly = poly.fit_transform(X_scaled)
                        model = LinearRegression()
                        model.fit(X_poly, y_clean)
                        y_pred = model.predict(X_poly)
                    else:
                        model.fit(X_scaled, y_clean)
                        y_pred = model.predict(X_scaled)
                    
                    # 計算指標
                    r2 = r2_score(y_clean, y_pred)
                    mse = mean_squared_error(y_clean, y_pred)
                    mae = mean_absolute_error(y_clean, y_pred)
                    
                    # 特徵重要性
                    if hasattr(model, 'feature_importances_'):
                        importance = model.feature_importances_
                        top_features = sorted(zip(best_factors, importance), key=lambda x: x[1], reverse=True)[:5]
                    else:
                        top_features = []
                    
                    nonlinear_results[target][model_name] = {
                        'r2': r2,
                        'mse': mse,
                        'mae': mae,
                        'top_features': top_features
                    }
                    
                    print(f"  {model_name:15s}: R²={r2:7.4f}, MSE={mse:10.6f}")
                    
                except Exception as e:
                    print(f"  {model_name:15s}: 失敗 - {str(e)[:50]}")
        
        self.results['nonlinear'] = nonlinear_results
        return nonlinear_results
    
    def dynamic_factor_analysis(self):
        """動態因子分析"""
        print("\n⏰ 動態因子分析...")
        
        # 按時間窗口分析因子效應變化
        window_size = 5000  # 約2個月的數據
        step_size = 1000   # 滑動步長
        
        dynamic_results = {}
        
        # 選擇最佳因子
        best_factor = None
        best_correlation = 0
        
        for target in ['return_1.0h']:  # 專注於1小時收益率
            if target in self.results['combinations']:
                sorted_factors = sorted(
                    self.results['combinations'][target].items(),
                    key=lambda x: x[1]['abs_correlation'],
                    reverse=True
                )
                if sorted_factors:
                    best_factor = sorted_factors[0][0]
                    best_correlation = sorted_factors[0][1]['abs_correlation']
                    break
        
        if best_factor is None:
            print("❌ 未找到有效因子")
            return {}
        
        print(f"分析最佳因子: {best_factor} (相關性: {best_correlation:.4f})")
        
        dynamic_correlations = []
        dynamic_r2_scores = []
        time_windows = []
        
        for start_idx in range(0, len(self.data) - window_size, step_size):
            end_idx = start_idx + window_size
            
            # 獲取時間窗口數據
            window_data = self.data.iloc[start_idx:end_idx]
            window_factor = self.factor_combinations_df[best_factor].iloc[start_idx:end_idx]
            window_target = window_data['return_1.0h']
            
            # 移除缺失值
            valid_data = pd.concat([window_factor, window_target], axis=1).dropna()
            
            if len(valid_data) < 100:
                continue
            
            try:
                # 計算相關性
                correlation = np.corrcoef(valid_data.iloc[:, 0], valid_data.iloc[:, 1])[0, 1]
                
                # 計算R²
                model = LinearRegression()
                X = valid_data.iloc[:, 0].values.reshape(-1, 1)
                y = valid_data.iloc[:, 1].values
                model.fit(X, y)
                r2 = model.score(X, y)
                
                dynamic_correlations.append(correlation)
                dynamic_r2_scores.append(r2)
                time_windows.append(window_data.index[len(window_data)//2])
                
            except Exception as e:
                continue
        
        dynamic_results = {
            'factor': best_factor,
            'time_windows': time_windows,
            'correlations': dynamic_correlations,
            'r2_scores': dynamic_r2_scores,
            'mean_correlation': np.mean(dynamic_correlations),
            'std_correlation': np.std(dynamic_correlations),
            'mean_r2': np.mean(dynamic_r2_scores),
            'std_r2': np.std(dynamic_r2_scores)
        }
        
        print(f"✅ 動態分析完成:")
        print(f"  平均相關性: {dynamic_results['mean_correlation']:.4f} ± {dynamic_results['std_correlation']:.4f}")
        print(f"  平均R²: {dynamic_results['mean_r2']:.4f} ± {dynamic_results['std_r2']:.4f}")
        
        self.results['dynamic'] = dynamic_results
        return dynamic_results
    
    def plot_dynamic_analysis(self):
        """繪製動態分析圖"""
        if 'dynamic' not in self.results:
            return
        
        dynamic_data = self.results['dynamic']
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
        
        # 相關性時間序列
        ax1.plot(dynamic_data['time_windows'], dynamic_data['correlations'], 'b-', linewidth=2, alpha=0.7)
        ax1.axhline(y=dynamic_data['mean_correlation'], color='r', linestyle='--', alpha=0.8, label=f'平均值: {dynamic_data["mean_correlation"]:.4f}')
        ax1.fill_between(dynamic_data['time_windows'], 
                        [dynamic_data['mean_correlation'] - dynamic_data['std_correlation']] * len(dynamic_data['time_windows']),
                        [dynamic_data['mean_correlation'] + dynamic_data['std_correlation']] * len(dynamic_data['time_windows']),
                        alpha=0.2, color='red')
        ax1.set_title(f'{dynamic_data["factor"]} 動態相關性分析', fontsize=14, fontweight='bold')
        ax1.set_ylabel('相關係數')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # R²時間序列
        ax2.plot(dynamic_data['time_windows'], dynamic_data['r2_scores'], 'g-', linewidth=2, alpha=0.7)
        ax2.axhline(y=dynamic_data['mean_r2'], color='r', linestyle='--', alpha=0.8, label=f'平均值: {dynamic_data["mean_r2"]:.4f}')
        ax2.fill_between(dynamic_data['time_windows'], 
                        [dynamic_data['mean_r2'] - dynamic_data['std_r2']] * len(dynamic_data['time_windows']),
                        [dynamic_data['mean_r2'] + dynamic_data['std_r2']] * len(dynamic_data['time_windows']),
                        alpha=0.2, color='red')
        ax2.set_title(f'{dynamic_data["factor"]} 動態預測能力分析', fontsize=14, fontweight='bold')
        ax2.set_ylabel('R² Score')
        ax2.set_xlabel('時間')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('dynamic_factor_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✅ 動態分析圖已保存: dynamic_factor_analysis.png")

def main():
    """主函數"""
    print("🚀 啟動高級因子組合和非線性關係分析")
    print("=" * 80)
    
    analyzer = AdvancedFactorAnalysis()
    
    # 載入數據
    if not analyzer.load_data():
        return
    
    # 創建因子組合
    analyzer.create_factor_combinations()
    
    # 分析因子組合
    analyzer.analyze_factor_combinations()
    
    # 非線性分析
    analyzer.nonlinear_analysis()
    
    # 動態因子分析
    analyzer.dynamic_factor_analysis()
    
    # 繪製動態分析圖
    analyzer.plot_dynamic_analysis()
    
    print("\n🎉 高級分析完成！")

if __name__ == "__main__":
    main()
