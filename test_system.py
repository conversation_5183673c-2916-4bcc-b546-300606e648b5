"""
CVD 策略系統測試文件
測試各個模塊的功能

作者: 專業量化策略工程師
"""

import asyncio
import os
import sys
from datetime import datetime

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cvd_strategy_core import CVDStrategyCore, TradingSignal
from database import CVDDatabase, TradeResult
from trade_monitor import TradeMonitor
from telegram_bot import TelegramBot

async def test_cvd_core():
    """測試 CVD 核心功能"""
    print("🧪 測試 CVD 策略核心...")
    
    try:
        async with CVDStrategyCore() as core:
            # 測試單個幣種信號檢測
            signal = await core.check_signals("BTCUSDT")
            if signal:
                print(f"✅ 檢測到信號: {signal.symbol} {signal.direction}")
                print(f"   入場價: ${signal.entry_price:.4f}")
                print(f"   止盈價: ${signal.take_profit:.4f}")
                print(f"   止損價: ${signal.stop_loss:.4f}")
            else:
                print("ℹ️ 當前無信號")
            
            # 測試所有幣種掃描
            all_signals = await core.scan_all_symbols()
            print(f"📊 掃描結果: 發現 {len(all_signals)} 個信號")
            
            for signal in all_signals:
                print(f"   {signal.symbol}: {signal.direction} @ ${signal.entry_price:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ CVD 核心測試失敗: {e}")
        return False

def test_database():
    """測試數據庫功能"""
    print("\n🧪 測試數據庫功能...")
    
    try:
        db = CVDDatabase("test_cvd_system.db")
        
        # 測試保存信號
        test_signal = TradingSignal(
            symbol="BTCUSDT",
            direction="LONG",
            entry_price=95000.0,
            take_profit=97000.0,
            stop_loss=93000.0,
            timestamp=datetime.now(),
            atr_value=1000.0,
            signal_id="TEST_SIGNAL_001"
        )
        
        success = db.save_signal(test_signal)
        print(f"保存信號: {'✅' if success else '❌'}")
        
        # 測試獲取活躍信號
        active_signals = db.get_active_signals()
        print(f"活躍信號數量: {len(active_signals)}")
        
        # 測試統計數據
        stats = db.get_trade_statistics()
        print(f"統計數據: 總信號 {stats['total_signals']}, 勝率 {stats['win_rate']:.2%}")
        
        return True
        
    except Exception as e:
        print(f"❌ 數據庫測試失敗: {e}")
        return False

async def test_trade_monitor():
    """測試交易監控"""
    print("\n🧪 測試交易監控...")
    
    try:
        db = CVDDatabase("test_cvd_system.db")
        
        async with TradeMonitor(db) as monitor:
            # 測試獲取價格
            price = await monitor.get_current_price("BTCUSDT")
            if price:
                print(f"✅ BTC 當前價格: ${price:,.2f}")
            else:
                print("❌ 價格獲取失敗")
                return False
            
            # 測試監控狀態
            status = monitor.get_monitoring_status()
            print(f"監控狀態: {status['active_monitors']} 個活躍監控")
        
        return True
        
    except Exception as e:
        print(f"❌ 交易監控測試失敗: {e}")
        return False

async def test_telegram_bot():
    """測試 Telegram 機器人"""
    print("\n🧪 測試 Telegram 機器人...")
    
    # 檢查環境變量
    bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
    chat_id = os.getenv("TELEGRAM_CHAT_ID")
    
    if not bot_token or not chat_id:
        print("⚠️ 跳過 Telegram 測試 - 未設置環境變量")
        print("   請設置 TELEGRAM_BOT_TOKEN 和 TELEGRAM_CHAT_ID")
        return True
    
    try:
        db = CVDDatabase("test_cvd_system.db")
        
        async with TelegramBot(bot_token, chat_id, db) as bot:
            # 測試發送消息
            success = await bot.send_message("🧪 CVD 系統測試消息")
            print(f"發送測試消息: {'✅' if success else '❌'}")
            
            # 測試發送系統狀態
            success = await bot.send_system_status()
            print(f"發送系統狀態: {'✅' if success else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Telegram 機器人測試失敗: {e}")
        return False

async def test_signal_formatting():
    """測試信號格式化"""
    print("\n🧪 測試信號格式化...")
    
    try:
        # 創建測試信號
        test_signal = TradingSignal(
            symbol="1000PEPEUSDT",
            direction="LONG",
            entry_price=0.00002345,
            take_profit=0.00002456,
            stop_loss=0.00002234,
            timestamp=datetime.now(),
            atr_value=0.00000111,
            signal_id="TEST_FORMAT_001"
        )
        
        # 測試格式化
        from cvd_strategy_core import CVDStrategyCore
        core = CVDStrategyCore()
        formatted_message = core.format_signal_for_telegram(test_signal)
        
        print("✅ 格式化信號:")
        print(formatted_message)
        
        return True
        
    except Exception as e:
        print(f"❌ 信號格式化測試失敗: {e}")
        return False

async def run_all_tests():
    """運行所有測試"""
    print("🚀 CVD 策略系統完整測試")
    print("=" * 60)
    
    test_results = []
    
    # 運行各項測試
    tests = [
        ("CVD 核心功能", test_cvd_core()),
        ("數據庫功能", test_database()),
        ("交易監控", test_trade_monitor()),
        ("Telegram 機器人", test_telegram_bot()),
        ("信號格式化", test_signal_formatting())
    ]
    
    for test_name, test_coro in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if asyncio.iscoroutine(test_coro):
                result = await test_coro
            else:
                result = test_coro
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
            test_results.append((test_name, False))
    
    # 顯示測試結果摘要
    print("\n" + "=" * 60)
    print("📊 測試結果摘要")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name:<20}: {status}")
        if result:
            passed += 1
    
    print(f"\n總計: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！系統準備就緒。")
    else:
        print("⚠️ 部分測試失敗，請檢查配置。")
    
    return passed == total

def main():
    """主函數"""
    print("🧪 CVD 策略系統測試工具")
    print("請確保已設置必要的環境變量:")
    print("- TELEGRAM_BOT_TOKEN")
    print("- TELEGRAM_CHAT_ID")
    print()
    
    # 運行測試
    result = asyncio.run(run_all_tests())
    
    if result:
        print("\n✅ 系統測試完成，可以開始部署！")
        sys.exit(0)
    else:
        print("\n❌ 系統測試失敗，請修復問題後重試。")
        sys.exit(1)

if __name__ == "__main__":
    main()
