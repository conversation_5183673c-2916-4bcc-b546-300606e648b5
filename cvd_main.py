"""
CVD 策略主程序
整合信號生成、交易監控、Telegram通知等所有功能

作者: 專業量化策略工程師
"""

import asyncio
import logging
import os
import signal
import sys
from datetime import datetime
from typing import Set

from cvd_strategy_core import CVDStrategyCore
from database import CVDDatabase
from trade_monitor import TradeMonitor
from telegram_bot import TelegramNotificationService

class CVDStrategySystem:
    """CVD 策略系統主控制器"""
    
    def __init__(self):
        # 設置日誌
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('cvd_strategy.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 初始化組件
        self.db = CVDDatabase()
        self.strategy_core = None
        self.trade_monitor = None
        self.telegram_service = None
        
        # 系統狀態
        self.is_running = False
        self.processed_signals: Set[str] = set()
        
        # 從環境變量獲取配置
        self.bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        self.chat_id = os.getenv("TELEGRAM_CHAT_ID")
        
        if not self.bot_token or not self.chat_id:
            self.logger.error("❌ 請設置 TELEGRAM_BOT_TOKEN 和 TELEGRAM_CHAT_ID 環境變量")
            sys.exit(1)
    
    async def start_system(self):
        """啟動系統"""
        self.logger.info("🚀 CVD策略系統啟動中...")
        self.is_running = True
        
        try:
            # 初始化組件
            self.strategy_core = CVDStrategyCore()
            self.telegram_service = TelegramNotificationService(
                self.bot_token, self.chat_id, self.db
            )
            
            # 啟動各個服務
            tasks = [
                asyncio.create_task(self.signal_generation_loop()),
                asyncio.create_task(self.trade_monitoring_loop()),
                asyncio.create_task(self.telegram_service.start()),
                asyncio.create_task(self.system_maintenance_loop())
            ]
            
            self.logger.info("✅ 所有服務已啟動")
            
            # 等待所有任務完成
            await asyncio.gather(*tasks)
            
        except Exception as e:
            self.logger.error(f"❌ 系統啟動失敗: {e}")
            raise
    
    async def signal_generation_loop(self):
        """信號生成循環"""
        self.logger.info("🎯 信號生成服務啟動")
        
        async with self.strategy_core:
            while self.is_running:
                try:
                    # 掃描所有幣種的信號
                    signals = await self.strategy_core.scan_all_symbols()
                    
                    for signal in signals:
                        # 檢查是否已處理過此信號
                        if signal.signal_id not in self.processed_signals:
                            # 保存信號到數據庫
                            success = self.db.save_signal(signal)
                            
                            if success:
                                # 發送Telegram通知
                                await self.telegram_service.notify_signal(signal)
                                
                                # 標記為已處理
                                self.processed_signals.add(signal.signal_id)
                                
                                self.logger.info(f"✅ 新信號已處理: {signal.signal_id}")
                            else:
                                self.logger.error(f"❌ 信號保存失敗: {signal.signal_id}")
                    
                    # 等待下次掃描 (每5分鐘掃描一次)
                    await asyncio.sleep(300)
                    
                except Exception as e:
                    self.logger.error(f"❌ 信號生成異常: {e}")
                    await asyncio.sleep(60)  # 出錯後等待1分鐘
    
    async def trade_monitoring_loop(self):
        """交易監控循環"""
        self.logger.info("📊 交易監控服務啟動")
        
        async with TradeMonitor(self.db) as monitor:
            self.trade_monitor = monitor
            
            # 啟動監控任務
            monitor_task = asyncio.create_task(monitor.start_monitoring())
            
            # 監控交易結果並發送通知
            result_notification_task = asyncio.create_task(
                self.trade_result_notification_loop()
            )
            
            await asyncio.gather(monitor_task, result_notification_task)
    
    async def trade_result_notification_loop(self):
        """交易結果通知循環"""
        last_check_time = datetime.now()
        
        while self.is_running:
            try:
                # 獲取最近的交易結果
                recent_trades = self.db.get_recent_trades(10)
                
                for trade in recent_trades:
                    trade_time = datetime.fromisoformat(trade['exit_time'])
                    
                    # 只處理新的交易結果
                    if trade_time > last_check_time:
                        # 創建TradeResult對象
                        from database import TradeResult
                        trade_result = TradeResult(
                            signal_id=trade['signal_id'],
                            symbol=trade['symbol'],
                            direction=trade['direction'],
                            entry_price=trade['entry_price'],
                            exit_price=trade['exit_price'],
                            entry_time=datetime.fromisoformat(trade['entry_time']),
                            exit_time=trade_time,
                            result_type=trade['result_type'],
                            pnl_percentage=trade['pnl_percentage'],
                            duration_minutes=trade['duration_minutes']
                        )
                        
                        # 發送Telegram通知
                        await self.telegram_service.notify_trade_result(trade_result)
                        
                        self.logger.info(f"✅ 交易結果通知已發送: {trade['signal_id']}")
                
                # 更新檢查時間
                last_check_time = datetime.now()
                
                # 等待下次檢查
                await asyncio.sleep(30)  # 每30秒檢查一次
                
            except Exception as e:
                self.logger.error(f"❌ 交易結果通知異常: {e}")
                await asyncio.sleep(60)
    
    async def system_maintenance_loop(self):
        """系統維護循環"""
        self.logger.info("🔧 系統維護服務啟動")
        
        while self.is_running:
            try:
                # 每小時執行一次維護
                await asyncio.sleep(3600)
                
                # 更新每日統計
                self.db.update_daily_stats()
                
                # 清理舊數據 (保留30天)
                self.db.cleanup_old_data(30)
                
                # 清理已處理的信號ID集合 (避免內存泄漏)
                if len(self.processed_signals) > 1000:
                    # 只保留最近的500個
                    recent_signals = list(self.processed_signals)[-500:]
                    self.processed_signals = set(recent_signals)
                
                self.logger.info("✅ 系統維護完成")
                
            except Exception as e:
                self.logger.error(f"❌ 系統維護異常: {e}")
    
    async def stop_system(self):
        """停止系統"""
        self.logger.info("🛑 正在停止CVD策略系統...")
        self.is_running = False
        
        # 等待一段時間讓任務正常結束
        await asyncio.sleep(5)
        
        self.logger.info("✅ CVD策略系統已停止")
    
    def setup_signal_handlers(self):
        """設置信號處理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"收到信號 {signum}，正在停止系統...")
            asyncio.create_task(self.stop_system())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

async def main():
    """主函數"""
    print("🚀 CVD策略雲端報單系統")
    print("=" * 50)
    print("支持幣種: 1000PEPE, TRB, DOGE, SOL, BTC")
    print("時間框架: 15分鐘")
    print("盈虧比: 2:1")
    print("=" * 50)
    
    # 創建系統實例
    system = CVDStrategySystem()
    
    # 設置信號處理器
    system.setup_signal_handlers()
    
    try:
        # 啟動系統
        await system.start_system()
    except KeyboardInterrupt:
        print("\n收到中斷信號，正在停止系統...")
        await system.stop_system()
    except Exception as e:
        print(f"❌ 系統運行異常: {e}")
        await system.stop_system()
        sys.exit(1)

if __name__ == "__main__":
    # 運行主程序
    asyncio.run(main())
