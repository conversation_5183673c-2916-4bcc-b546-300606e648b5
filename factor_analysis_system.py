"""
Blave 因子分析系統
數據預處理、對齊和多因子迴歸分析

功能:
1. 數據清洗和時間對齊
2. 多因子迴歸分析
3. 因子重要性評估
4. 預測能力分析
5. 可視化報告生成

作者: 專業量化策略工程師
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score, mean_squared_error
from sklearn.preprocessing import StandardScaler
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 設置中文字體
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class FactorAnalysisSystem:
    """因子分析系統"""
    
    def __init__(self):
        self.price_data = None
        self.factor_data = {}
        self.combined_data = None
        self.analysis_results = {}
        
    def load_data(self):
        """載入所有數據"""
        print("📊 載入數據...")
        
        # 載入價格數據
        try:
            self.price_data = pd.read_csv('binance_data/BTCUSDT_15m_enhanced.csv', index_col=0, parse_dates=True)
            print(f"✅ 價格數據載入成功: {len(self.price_data)} 條記錄")
            print(f"   時間範圍: {self.price_data.index.min()} 到 {self.price_data.index.max()}")
        except Exception as e:
            print(f"❌ 價格數據載入失敗: {e}")
            return False
        
        # 載入因子數據
        factor_files = {
            'holder_concentration': 'blave_factor_data/holder_concentration_BTCUSDT_15min.csv',
            'whale_hunter': 'blave_factor_data/whale_hunter_BTCUSDT_15min.csv',
            'taker_intensity': 'blave_factor_data/taker_intensity_BTCUSDT_15min.csv'
        }
        
        for factor_name, file_path in factor_files.items():
            try:
                factor_df = pd.read_csv(file_path, index_col=0, parse_dates=True)
                self.factor_data[factor_name] = factor_df
                print(f"✅ {factor_name} 數據載入成功: {len(factor_df)} 條記錄")
            except Exception as e:
                print(f"❌ {factor_name} 數據載入失敗: {e}")
        
        print(f"📋 成功載入 {len(self.factor_data)} 個因子")
        return True
    
    def preprocess_and_align_data(self):
        """數據預處理和時間對齊"""
        print("\n🔧 數據預處理和對齊...")
        
        if self.price_data is None or not self.factor_data:
            print("❌ 數據未載入")
            return False
        
        # 以價格數據為基準進行對齊
        aligned_data = self.price_data.copy()
        
        # 逐個合併因子數據
        for factor_name, factor_df in self.factor_data.items():
            # 使用 merge_asof 進行時間對齊（向前填充）
            aligned_data = pd.merge_asof(
                aligned_data.sort_index(),
                factor_df.sort_index(),
                left_index=True,
                right_index=True,
                direction='backward'
            )
        
        # 計算目標變量（未來收益率）
        aligned_data['future_return_1h'] = aligned_data['Close'].shift(-4).pct_change()  # 1小時後收益率
        aligned_data['future_return_4h'] = aligned_data['Close'].shift(-16).pct_change()  # 4小時後收益率
        aligned_data['future_return_1d'] = aligned_data['Close'].shift(-96).pct_change()  # 1天後收益率
        
        # 計算當前收益率
        aligned_data['current_return'] = aligned_data['Close'].pct_change()
        
        # 移除缺失值
        aligned_data = aligned_data.dropna()
        
        self.combined_data = aligned_data
        print(f"✅ 數據對齊完成: {len(aligned_data)} 條有效記錄")
        print(f"   時間範圍: {aligned_data.index.min()} 到 {aligned_data.index.max()}")
        
        return True
    
    def calculate_correlations(self):
        """計算相關性分析"""
        print("\n📈 計算相關性分析...")
        
        if self.combined_data is None:
            print("❌ 數據未對齊")
            return
        
        # 選擇因子列
        factor_columns = [col for col in self.combined_data.columns if '_alpha' in col]
        target_columns = ['future_return_1h', 'future_return_4h', 'future_return_1d', 'current_return']
        
        # 計算相關性矩陣
        correlation_data = self.combined_data[factor_columns + target_columns]
        correlation_matrix = correlation_data.corr()
        
        # 提取因子與收益率的相關性
        factor_correlations = {}
        for target in target_columns:
            factor_correlations[target] = correlation_matrix[target][factor_columns].abs().sort_values(ascending=False)
        
        self.analysis_results['correlations'] = factor_correlations
        self.analysis_results['correlation_matrix'] = correlation_matrix
        
        print("✅ 相關性分析完成")
        
        # 顯示結果
        for target, corr in factor_correlations.items():
            print(f"\n{target} 相關性排名:")
            for factor, value in corr.items():
                print(f"  {factor}: {value:.4f}")
    
    def perform_regression_analysis(self):
        """執行多因子迴歸分析"""
        print("\n🔬 執行多因子迴歸分析...")
        
        if self.combined_data is None:
            print("❌ 數據未對齊")
            return
        
        factor_columns = [col for col in self.combined_data.columns if '_alpha' in col]
        target_columns = ['future_return_1h', 'future_return_4h', 'future_return_1d']
        
        regression_results = {}
        
        for target in target_columns:
            print(f"\n分析目標: {target}")
            
            # 準備數據
            X = self.combined_data[factor_columns].fillna(0)
            y = self.combined_data[target].fillna(0)
            
            # 標準化特徵
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            
            # 執行線性迴歸
            model = LinearRegression()
            model.fit(X_scaled, y)
            
            # 預測
            y_pred = model.predict(X_scaled)
            
            # 計算指標
            r2 = r2_score(y, y_pred)
            mse = mean_squared_error(y, y_pred)
            
            # 計算因子重要性（絕對係數值）
            feature_importance = pd.Series(
                np.abs(model.coef_), 
                index=factor_columns
            ).sort_values(ascending=False)
            
            # 計算 t 統計量和 p 值
            n = len(y)
            k = len(factor_columns)
            residuals = y - y_pred
            mse_residual = np.sum(residuals**2) / (n - k - 1)
            
            # 計算標準誤差
            X_scaled_df = pd.DataFrame(X_scaled, columns=factor_columns)
            var_coef = mse_residual * np.linalg.inv(X_scaled_df.T @ X_scaled_df).diagonal()
            std_err = np.sqrt(var_coef)
            
            # t 統計量和 p 值
            t_stats = model.coef_ / std_err
            p_values = 2 * (1 - stats.t.cdf(np.abs(t_stats), n - k - 1))
            
            regression_results[target] = {
                'model': model,
                'scaler': scaler,
                'r2_score': r2,
                'mse': mse,
                'feature_importance': feature_importance,
                'coefficients': pd.Series(model.coef_, index=factor_columns),
                't_statistics': pd.Series(t_stats, index=factor_columns),
                'p_values': pd.Series(p_values, index=factor_columns),
                'predictions': y_pred,
                'actual': y
            }
            
            print(f"  R² Score: {r2:.4f}")
            print(f"  MSE: {mse:.6f}")
            print(f"  因子重要性排名:")
            for factor, importance in feature_importance.items():
                p_val = p_values[factor_columns.index(factor)]
                significance = "***" if p_val < 0.001 else "**" if p_val < 0.01 else "*" if p_val < 0.05 else ""
                print(f"    {factor}: {importance:.4f} (p={p_val:.4f}){significance}")
        
        self.analysis_results['regression'] = regression_results
        print("\n✅ 迴歸分析完成")
    
    def generate_summary_report(self):
        """生成綜合分析報告"""
        print("\n📋 生成綜合分析報告...")
        
        if 'regression' not in self.analysis_results:
            print("❌ 迴歸分析未完成")
            return
        
        # 創建報告
        report = []
        report.append("=" * 80)
        report.append("📊 Blave 因子影響力分析報告")
        report.append("=" * 80)
        report.append(f"分析時間: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"數據範圍: {self.combined_data.index.min()} 到 {self.combined_data.index.max()}")
        report.append(f"數據記錄: {len(self.combined_data):,} 條")
        report.append("")
        
        # 因子概覽
        report.append("📈 因子概覽:")
        factor_columns = [col for col in self.combined_data.columns if '_alpha' in col]
        for i, factor in enumerate(factor_columns, 1):
            report.append(f"  {i}. {factor}")
        report.append("")
        
        # 迴歸分析結果
        report.append("🔬 多因子迴歸分析結果:")
        report.append("")
        
        for target, results in self.analysis_results['regression'].items():
            report.append(f"目標變量: {target}")
            report.append(f"  預測能力 (R²): {results['r2_score']:.4f}")
            report.append(f"  預測誤差 (MSE): {results['mse']:.6f}")
            report.append("")
            
            report.append("  因子重要性排名:")
            for i, (factor, importance) in enumerate(results['feature_importance'].items(), 1):
                coef = results['coefficients'][factor]
                p_val = results['p_values'][factor]
                significance = "***" if p_val < 0.001 else "**" if p_val < 0.01 else "*" if p_val < 0.05 else ""
                direction = "正向" if coef > 0 else "負向"
                report.append(f"    {i}. {factor}: {importance:.4f} ({direction}, p={p_val:.4f}){significance}")
            report.append("")
        
        # 總結
        report.append("📊 總結:")
        
        # 找出最重要的因子
        all_importance = {}
        for target, results in self.analysis_results['regression'].items():
            for factor, importance in results['feature_importance'].items():
                if factor not in all_importance:
                    all_importance[factor] = []
                all_importance[factor].append(importance)
        
        # 計算平均重要性
        avg_importance = {factor: np.mean(values) for factor, values in all_importance.items()}
        sorted_factors = sorted(avg_importance.items(), key=lambda x: x[1], reverse=True)
        
        report.append("  綜合因子重要性排名 (平均重要性):")
        for i, (factor, avg_imp) in enumerate(sorted_factors, 1):
            report.append(f"    {i}. {factor}: {avg_imp:.4f}")
        
        report.append("")
        report.append("=" * 80)
        
        # 保存報告
        report_text = "\n".join(report)
        with open("factor_analysis_report.txt", "w", encoding="utf-8") as f:
            f.write(report_text)
        
        print("✅ 報告已保存到: factor_analysis_report.txt")
        print("\n" + report_text)
        
        return report_text

def main():
    """主函數"""
    print("🚀 啟動 Blave 因子分析系統")
    print("=" * 60)
    
    # 創建分析系統
    analyzer = FactorAnalysisSystem()
    
    # 載入數據
    if not analyzer.load_data():
        print("❌ 數據載入失敗，程序退出")
        return
    
    # 數據預處理和對齊
    if not analyzer.preprocess_and_align_data():
        print("❌ 數據預處理失敗，程序退出")
        return
    
    # 相關性分析
    analyzer.calculate_correlations()
    
    # 迴歸分析
    analyzer.perform_regression_analysis()
    
    # 生成報告
    analyzer.generate_summary_report()
    
    print("\n🎉 分析完成！")

if __name__ == "__main__":
    main()
