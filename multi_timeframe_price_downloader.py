"""
多時框價格數據下載器
支持 5分鐘、15分鐘、1小時三個時框的 BTC 永續合約價格數據下載
包含 ATR 指標計算

作者: 專業量化策略工程師
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import json
from typing import Dict, List, Optional
import logging

class MultiTimeframePriceDownloader:
    """多時框價格數據下載器"""
    
    def __init__(self):
        # 幣安期貨 API 配置
        self.base_url = "https://fapi.binance.com"
        
        # 時框配置
        self.timeframes = {
            '5min': {
                'interval': '5m',
                'description': '5分鐘時框',
                'atr_period': 14
            },
            '15min': {
                'interval': '15m',
                'description': '15分鐘時框',
                'atr_period': 14
            },
            '1h': {
                'interval': '1h',
                'description': '1小時時框',
                'atr_period': 14
            }
        }
        
        # 設置日誌
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 創建數據存儲目錄
        self.data_dir = "multi_timeframe_price_data"
        os.makedirs(self.data_dir, exist_ok=True)
        
        self.session = None
    
    async def __aenter__(self):
        """異步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """異步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    def date_to_timestamp(self, date_str: str) -> int:
        """將日期字符串轉換為毫秒時間戳"""
        dt = pd.to_datetime(date_str)
        return int(dt.timestamp() * 1000)
    
    async def get_price_data(self, timeframe: str, symbol: str = "BTCUSDT",
                            start_date: str = "2023-12-31", 
                            end_date: str = "2025-06-30") -> Optional[pd.DataFrame]:
        """獲取指定時框的價格數據"""
        try:
            interval = self.timeframes[timeframe]['interval']
            description = self.timeframes[timeframe]['description']
            
            self.logger.info(f"🔗 獲取 {description} 價格數據")
            
            start_timestamp = self.date_to_timestamp(start_date)
            end_timestamp = self.date_to_timestamp(end_date)
            
            all_data = []
            current_start = start_timestamp
            
            # 幣安API每次最多返回1500條數據，需要分批獲取
            batch_count = 0
            while current_start < end_timestamp:
                url = f"{self.base_url}/fapi/v1/klines"
                params = {
                    "symbol": symbol,
                    "interval": interval,
                    "startTime": current_start,
                    "endTime": end_timestamp,
                    "limit": 1500
                }
                
                batch_count += 1
                if batch_count % 10 == 1:  # 每10批顯示一次進度
                    self.logger.info(f"📥 {description} 獲取第 {batch_count} 批數據...")
                
                async with self.session.get(url, params=params, timeout=30) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if data:
                            all_data.extend(data)
                            
                            # 更新下一批的開始時間
                            last_timestamp = int(data[-1][0])
                            current_start = last_timestamp + 1
                        else:
                            break
                    else:
                        self.logger.error(f"❌ {description} API響應異常: {response.status}")
                        break
                
                # 避免請求過於頻繁
                await asyncio.sleep(0.1)
            
            if all_data:
                # 轉換為 DataFrame
                df = pd.DataFrame(all_data, columns=[
                    'timestamp', 'open', 'high', 'low', 'close', 'volume',
                    'close_time', 'quote_asset_volume', 'number_of_trades',
                    'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
                ])
                
                # 轉換數據類型
                numeric_columns = ['open', 'high', 'low', 'close', 'volume']
                for col in numeric_columns:
                    df[col] = pd.to_numeric(df[col])
                
                # 轉換時間戳
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                df.set_index('timestamp', inplace=True)
                df = df.sort_index()
                
                # 去重
                df = df[~df.index.duplicated(keep='first')]
                
                # 重命名主要列
                df = df.rename(columns={
                    'open': 'Open',
                    'high': 'High',
                    'low': 'Low',
                    'close': 'Close',
                    'volume': 'Volume'
                })
                
                self.logger.info(f"✅ {description} 價格數據獲取成功: {len(df)} 條記錄")
                self.logger.info(f"   時間範圍: {df.index.min()} 到 {df.index.max()}")
                self.logger.info(f"   價格範圍: ${df['Low'].min():.2f} - ${df['High'].max():.2f}")
                
                return df
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ {timeframe} 價格數據獲取失敗: {e}")
            return None
    
    def calculate_atr(self, price_data: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """計算 ATR (Average True Range) 指標"""
        try:
            atr_period = self.timeframes[timeframe]['atr_period']
            description = self.timeframes[timeframe]['description']
            
            self.logger.info(f"📊 計算 {description} ATR指標 (週期={atr_period})")
            
            df = price_data.copy()
            
            # 計算 True Range
            df['prev_close'] = df['Close'].shift(1)
            df['tr1'] = df['High'] - df['Low']
            df['tr2'] = abs(df['High'] - df['prev_close'])
            df['tr3'] = abs(df['Low'] - df['prev_close'])
            df['true_range'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
            
            # 計算 ATR (使用 EMA)
            df['atr'] = df['true_range'].ewm(span=atr_period).mean()
            
            # 計算 ATR 百分比
            df['atr_pct'] = (df['atr'] / df['Close']) * 100
            
            # 清理臨時列
            df = df.drop(['prev_close', 'tr1', 'tr2', 'tr3'], axis=1)
            
            # 移除初始的NaN值
            df = df.dropna()
            
            self.logger.info(f"✅ {description} ATR計算完成: {len(df)} 條有效記錄")
            self.logger.info(f"   平均ATR: ${df['atr'].mean():.2f}")
            self.logger.info(f"   平均ATR%: {df['atr_pct'].mean():.3f}%")
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ {timeframe} ATR計算失敗: {e}")
            return price_data
    
    async def download_all_timeframes(self, symbol: str = "BTCUSDT",
                                     start_date: str = "2023-12-31",
                                     end_date: str = "2025-06-30") -> Dict[str, pd.DataFrame]:
        """下載所有時框的價格數據"""
        self.logger.info(f"🚀 開始下載多時框價格數據: {symbol}")
        self.logger.info(f"📅 時間範圍: {start_date} 到 {end_date}")
        
        all_price_data = {}
        
        for timeframe in self.timeframes.keys():
            self.logger.info(f"\n📥 正在下載: {self.timeframes[timeframe]['description']}")
            
            price_data = await self.get_price_data(
                timeframe=timeframe,
                symbol=symbol,
                start_date=start_date,
                end_date=end_date
            )
            
            if price_data is not None:
                # 計算 ATR 指標
                enhanced_data = self.calculate_atr(price_data, timeframe)
                all_price_data[timeframe] = enhanced_data
                
                # 保存到文件
                filename = f"{self.data_dir}/Price_{symbol}_{timeframe}.csv"
                enhanced_data.to_csv(filename)
                self.logger.info(f"💾 {timeframe} 數據已保存到: {filename}")
            else:
                self.logger.warning(f"⚠️ {timeframe} 數據獲取失敗")
            
            # 避免請求過於頻繁
            await asyncio.sleep(1)
        
        self.logger.info(f"\n✅ 多時框價格數據下載完成，成功獲取 {len(all_price_data)}/{len(self.timeframes)} 個時框")
        return all_price_data
    
    def save_summary(self, all_price_data: Dict[str, pd.DataFrame], symbol: str = "BTCUSDT"):
        """保存數據摘要"""
        summary = {
            "symbol": symbol,
            "download_time": datetime.now().isoformat(),
            "timeframes": {}
        }
        
        for timeframe, data in all_price_data.items():
            config = self.timeframes[timeframe]
            summary["timeframes"][timeframe] = {
                "description": config['description'],
                "interval": config['interval'],
                "atr_period": config['atr_period'],
                "records_count": len(data),
                "date_range": {
                    "start": data.index.min().isoformat(),
                    "end": data.index.max().isoformat()
                },
                "price_stats": {
                    "min_price": float(data['Low'].min()),
                    "max_price": float(data['High'].max()),
                    "avg_price": float(data['Close'].mean()),
                    "avg_atr": float(data['atr'].mean()),
                    "avg_atr_pct": float(data['atr_pct'].mean())
                },
                "columns": list(data.columns)
            }
        
        summary_file = f"{self.data_dir}/multi_timeframe_price_summary_{symbol}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"📋 多時框價格數據摘要已保存到: {summary_file}")
        return summary

async def main():
    """主函數 - 下載多時框價格數據"""
    async with MultiTimeframePriceDownloader() as downloader:
        # 下載所有時框的價格數據
        all_data = await downloader.download_all_timeframes(
            symbol="BTCUSDT",
            start_date="2023-12-31",
            end_date="2025-06-30"
        )
        
        # 保存摘要
        summary = downloader.save_summary(all_data, "BTCUSDT")
        
        print("\n" + "="*80)
        print("📊 多時框價格數據下載完成!")
        print("="*80)
        print(f"成功下載時框數量: {len(all_data)}")
        
        for timeframe, data in all_data.items():
            config = downloader.timeframes[timeframe]
            print(f"\n✅ {config['description']}:")
            print(f"   記錄數量: {len(data):,}")
            print(f"   時間範圍: {data.index.min()} 到 {data.index.max()}")
            print(f"   價格範圍: ${data['Low'].min():.2f} - ${data['High'].max():.2f}")
            print(f"   平均ATR: ${data['atr'].mean():.2f} ({data['atr_pct'].mean():.3f}%)")
        
        print("="*80)

if __name__ == "__main__":
    asyncio.run(main())
