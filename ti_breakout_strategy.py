"""
Taker Intensity 突破交易策略
基於自定義 TI 布林帶指標的多時框回測系統

策略邏輯:
1. TI 突破 70 分位數時做多
2. 使用 1.5 倍 ATR 作為止盈目標
3. 使用 1.0 倍 ATR 作為止損目標
4. 支持 5分鐘、15分鐘、1小時三個時框

作者: 專業量化策略工程師
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
import json
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class TIBreakoutStrategy:
    """TI 突破交易策略"""
    
    def __init__(self):
        self.timeframes = ['5min', '15min', '1h']
        self.results = {}
        self.trades_records = {}
        
        # 策略參數
        self.profit_ratio = 1.5  # 止盈倍數
        self.loss_ratio = 1.0    # 止損倍數
        
        # 創建結果目錄
        self.results_dir = "ti_breakout_results"
        os.makedirs(self.results_dir, exist_ok=True)
    
    def load_data(self, timeframe: str) -> <PERSON>ple[pd.DataFrame, pd.DataFrame]:
        """載入指定時框的 TI 和價格數據"""
        try:
            # 載入 TI 數據
            ti_file = f"multi_timeframe_ti_data/TI_Enhanced_{timeframe}_BTCUSDT.csv"
            ti_data = pd.read_csv(ti_file, index_col=0, parse_dates=True)
            
            # 載入價格數據
            price_file = f"multi_timeframe_price_data/Price_BTCUSDT_{timeframe}.csv"
            price_data = pd.read_csv(price_file, index_col=0, parse_dates=True)
            
            print(f"✅ {timeframe} 數據載入成功:")
            print(f"   TI數據: {len(ti_data)} 條記錄")
            print(f"   價格數據: {len(price_data)} 條記錄")
            
            return ti_data, price_data
            
        except Exception as e:
            print(f"❌ {timeframe} 數據載入失敗: {e}")
            return None, None
    
    def merge_data(self, ti_data: pd.DataFrame, price_data: pd.DataFrame) -> pd.DataFrame:
        """合併 TI 和價格數據"""
        try:
            # 使用時間對齊合併數據
            merged = pd.merge_asof(
                price_data.sort_index(),
                ti_data.sort_index(),
                left_index=True,
                right_index=True,
                direction='backward'
            )
            
            # 移除缺失值
            merged = merged.dropna()
            
            print(f"   合併後數據: {len(merged)} 條有效記錄")
            return merged
            
        except Exception as e:
            print(f"❌ 數據合併失敗: {e}")
            return pd.DataFrame()
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信號"""
        try:
            df = data.copy()
            
            # 生成做多信號：TI 突破上軌（70分位數）
            df['long_signal'] = (
                (df['taker_intensity'] > df['ti_upper']) & 
                (df['taker_intensity'].shift(1) <= df['ti_upper'].shift(1))
            ).astype(int)
            
            # 計算止盈止損價格
            df['entry_price'] = df['Close']
            df['stop_loss'] = df['entry_price'] - (df['atr'] * self.loss_ratio)
            df['take_profit'] = df['entry_price'] + (df['atr'] * self.profit_ratio)
            
            # 計算風險回報比
            df['risk_reward_ratio'] = (df['take_profit'] - df['entry_price']) / (df['entry_price'] - df['stop_loss'])
            
            print(f"   生成信號數量: {df['long_signal'].sum()}")
            
            return df
            
        except Exception as e:
            print(f"❌ 信號生成失敗: {e}")
            return data
    
    def backtest_strategy(self, data: pd.DataFrame, timeframe: str) -> Dict:
        """執行策略回測"""
        try:
            print(f"\n🔬 執行 {timeframe} 回測...")
            
            trades = []
            current_position = None
            
            for i, (timestamp, row) in enumerate(data.iterrows()):
                # 檢查開倉信號
                if row['long_signal'] == 1 and current_position is None:
                    current_position = {
                        'entry_time': timestamp,
                        'entry_price': row['Close'],
                        'stop_loss': row['stop_loss'],
                        'take_profit': row['take_profit'],
                        'atr': row['atr'],
                        'ti_value': row['taker_intensity'],
                        'ti_upper': row['ti_upper']
                    }
                
                # 檢查平倉條件
                elif current_position is not None:
                    exit_reason = None
                    exit_price = None
                    
                    # 檢查止盈
                    if row['High'] >= current_position['take_profit']:
                        exit_reason = 'take_profit'
                        exit_price = current_position['take_profit']
                    
                    # 檢查止損
                    elif row['Low'] <= current_position['stop_loss']:
                        exit_reason = 'stop_loss'
                        exit_price = current_position['stop_loss']
                    
                    # 如果觸發平倉條件
                    if exit_reason:
                        pnl = exit_price - current_position['entry_price']
                        pnl_pct = (pnl / current_position['entry_price']) * 100
                        
                        trade_record = {
                            'entry_time': current_position['entry_time'],
                            'exit_time': timestamp,
                            'entry_price': current_position['entry_price'],
                            'exit_price': exit_price,
                            'stop_loss': current_position['stop_loss'],
                            'take_profit': current_position['take_profit'],
                            'pnl': pnl,
                            'pnl_pct': pnl_pct,
                            'exit_reason': exit_reason,
                            'duration_hours': (timestamp - current_position['entry_time']).total_seconds() / 3600,
                            'atr': current_position['atr'],
                            'ti_value': current_position['ti_value'],
                            'ti_upper': current_position['ti_upper']
                        }
                        
                        trades.append(trade_record)
                        current_position = None
            
            # 轉換為 DataFrame
            trades_df = pd.DataFrame(trades)
            
            if len(trades_df) == 0:
                print(f"⚠️ {timeframe} 沒有完成的交易")
                return self._empty_results()
            
            # 計算績效指標
            results = self._calculate_performance_metrics(trades_df, timeframe)
            
            # 保存交易記錄
            trades_file = f"{self.results_dir}/trades_{timeframe}.csv"
            trades_df.to_csv(trades_file, index=False)
            print(f"💾 {timeframe} 交易記錄已保存: {trades_file}")
            
            return results
            
        except Exception as e:
            print(f"❌ {timeframe} 回測失敗: {e}")
            return self._empty_results()
    
    def _calculate_performance_metrics(self, trades_df: pd.DataFrame, timeframe: str) -> Dict:
        """計算績效指標"""
        try:
            total_trades = len(trades_df)
            winning_trades = len(trades_df[trades_df['pnl'] > 0])
            losing_trades = len(trades_df[trades_df['pnl'] < 0])
            
            # 基本指標
            win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
            
            # 盈虧統計
            total_pnl = trades_df['pnl'].sum()
            total_pnl_pct = trades_df['pnl_pct'].sum()
            avg_win = trades_df[trades_df['pnl'] > 0]['pnl_pct'].mean() if winning_trades > 0 else 0
            avg_loss = trades_df[trades_df['pnl'] < 0]['pnl_pct'].mean() if losing_trades > 0 else 0
            
            # 盈利因子
            gross_profit = trades_df[trades_df['pnl'] > 0]['pnl_pct'].sum()
            gross_loss = abs(trades_df[trades_df['pnl'] < 0]['pnl_pct'].sum())
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
            
            # 最大回撤
            cumulative_returns = trades_df['pnl_pct'].cumsum()
            running_max = cumulative_returns.expanding().max()
            drawdown = cumulative_returns - running_max
            max_drawdown = drawdown.min()
            
            # Sharpe 比率 (假設無風險利率為 0)
            returns_std = trades_df['pnl_pct'].std()
            sharpe_ratio = (trades_df['pnl_pct'].mean() / returns_std) if returns_std > 0 else 0
            
            # 平均持倉時間
            avg_duration = trades_df['duration_hours'].mean()
            
            # 連續盈虧統計
            trades_df['win'] = (trades_df['pnl'] > 0).astype(int)
            consecutive_wins = self._max_consecutive(trades_df['win'].tolist(), 1)
            consecutive_losses = self._max_consecutive(trades_df['win'].tolist(), 0)
            
            results = {
                'timeframe': timeframe,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'total_pnl_pct': total_pnl_pct,
                'avg_win_pct': avg_win,
                'avg_loss_pct': avg_loss,
                'profit_factor': profit_factor,
                'max_drawdown_pct': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'avg_duration_hours': avg_duration,
                'max_consecutive_wins': consecutive_wins,
                'max_consecutive_losses': consecutive_losses,
                'gross_profit_pct': gross_profit,
                'gross_loss_pct': gross_loss
            }
            
            print(f"📊 {timeframe} 績效指標:")
            print(f"   總交易數: {total_trades}")
            print(f"   勝率: {win_rate:.2f}%")
            print(f"   總收益: {total_pnl_pct:.2f}%")
            print(f"   盈利因子: {profit_factor:.2f}")
            print(f"   最大回撤: {max_drawdown:.2f}%")
            print(f"   Sharpe比率: {sharpe_ratio:.2f}")
            
            return results
            
        except Exception as e:
            print(f"❌ 績效計算失敗: {e}")
            return self._empty_results()
    
    def _max_consecutive(self, lst: List, value: int) -> int:
        """計算連續出現某值的最大次數"""
        max_count = 0
        current_count = 0
        
        for item in lst:
            if item == value:
                current_count += 1
                max_count = max(max_count, current_count)
            else:
                current_count = 0
        
        return max_count
    
    def _empty_results(self) -> Dict:
        """返回空結果"""
        return {
            'timeframe': '',
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'win_rate': 0,
            'total_pnl_pct': 0,
            'avg_win_pct': 0,
            'avg_loss_pct': 0,
            'profit_factor': 0,
            'max_drawdown_pct': 0,
            'sharpe_ratio': 0,
            'avg_duration_hours': 0,
            'max_consecutive_wins': 0,
            'max_consecutive_losses': 0,
            'gross_profit_pct': 0,
            'gross_loss_pct': 0
        }
    
    def run_multi_timeframe_backtest(self):
        """執行多時框回測"""
        print("🚀 啟動 TI 突破策略多時框回測")
        print("="*60)
        
        all_results = []
        
        for timeframe in self.timeframes:
            print(f"\n📊 處理 {timeframe} 時框...")
            
            # 載入數據
            ti_data, price_data = self.load_data(timeframe)
            
            if ti_data is None or price_data is None:
                continue
            
            # 合併數據
            merged_data = self.merge_data(ti_data, price_data)
            
            if merged_data.empty:
                continue
            
            # 生成信號
            signal_data = self.generate_signals(merged_data)
            
            # 執行回測
            results = self.backtest_strategy(signal_data, timeframe)
            
            if results['total_trades'] > 0:
                all_results.append(results)
                self.results[timeframe] = results
        
        # 生成綜合報告
        if all_results:
            self.generate_comprehensive_report(all_results)
        
        print("\n🎉 多時框回測完成！")
        return all_results

    def generate_comprehensive_report(self, all_results: List[Dict]):
        """生成綜合分析報告"""
        try:
            print("\n📋 生成綜合分析報告...")

            # 創建結果 DataFrame
            results_df = pd.DataFrame(all_results)

            # 保存詳細結果
            results_file = f"{self.results_dir}/comprehensive_results.csv"
            results_df.to_csv(results_file, index=False)

            # 生成文字報告
            report = []
            report.append("="*80)
            report.append("📊 TI 突破策略 - 多時框回測報告")
            report.append("="*80)
            report.append(f"回測時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report.append(f"策略參數: 止盈 {self.profit_ratio}x ATR, 止損 {self.loss_ratio}x ATR")
            report.append("")

            # 各時框詳細結果
            report.append("📈 各時框詳細結果:")
            report.append("")

            for result in all_results:
                tf = result['timeframe']
                report.append(f"🔸 {tf} 時框:")
                report.append(f"   總交易數: {result['total_trades']}")
                report.append(f"   勝率: {result['win_rate']:.2f}%")
                report.append(f"   總收益: {result['total_pnl_pct']:.2f}%")
                report.append(f"   平均盈利: {result['avg_win_pct']:.2f}%")
                report.append(f"   平均虧損: {result['avg_loss_pct']:.2f}%")
                report.append(f"   盈利因子: {result['profit_factor']:.2f}")
                report.append(f"   最大回撤: {result['max_drawdown_pct']:.2f}%")
                report.append(f"   Sharpe比率: {result['sharpe_ratio']:.2f}")
                report.append(f"   平均持倉: {result['avg_duration_hours']:.1f}小時")
                report.append("")

            # 時框對比分析
            report.append("📊 時框對比分析:")
            report.append("")

            # 找出最佳時框
            best_sharpe = results_df.loc[results_df['sharpe_ratio'].idxmax()]
            best_profit = results_df.loc[results_df['total_pnl_pct'].idxmax()]
            best_winrate = results_df.loc[results_df['win_rate'].idxmax()]

            report.append(f"🏆 最佳 Sharpe 比率: {best_sharpe['timeframe']} ({best_sharpe['sharpe_ratio']:.2f})")
            report.append(f"💰 最高總收益: {best_profit['timeframe']} ({best_profit['total_pnl_pct']:.2f}%)")
            report.append(f"🎯 最高勝率: {best_winrate['timeframe']} ({best_winrate['win_rate']:.2f}%)")
            report.append("")

            # 策略評估
            report.append("📝 策略評估:")
            avg_winrate = results_df['win_rate'].mean()
            avg_profit_factor = results_df['profit_factor'].mean()
            avg_sharpe = results_df['sharpe_ratio'].mean()

            report.append(f"   平均勝率: {avg_winrate:.2f}%")
            report.append(f"   平均盈利因子: {avg_profit_factor:.2f}")
            report.append(f"   平均Sharpe比率: {avg_sharpe:.2f}")
            report.append("")

            # 建議
            report.append("💡 策略建議:")
            if avg_winrate >= 50 and avg_profit_factor >= 1.2:
                report.append("   ✅ 策略表現良好，建議進一步優化參數")
            elif avg_winrate >= 40:
                report.append("   ⚠️ 策略有潛力，建議調整止盈止損比例")
            else:
                report.append("   ❌ 策略需要重大改進，考慮其他信號組合")

            report.append("")
            report.append("="*80)

            # 保存報告
            report_text = "\n".join(report)
            report_file = f"{self.results_dir}/strategy_report.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_text)

            print(f"✅ 綜合報告已保存: {report_file}")
            print("\n" + report_text)

            # 生成可視化圖表
            self.create_performance_charts(results_df)

        except Exception as e:
            print(f"❌ 報告生成失敗: {e}")

    def create_performance_charts(self, results_df: pd.DataFrame):
        """創建績效圖表"""
        try:
            print("📊 生成績效圖表...")

            # 設置圖表樣式
            plt.style.use('default')
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle('TI Breakout Strategy - Multi-Timeframe Performance', fontsize=16, fontweight='bold')

            # 1. 勝率對比
            axes[0, 0].bar(results_df['timeframe'], results_df['win_rate'], color='skyblue', alpha=0.7)
            axes[0, 0].set_title('Win Rate by Timeframe')
            axes[0, 0].set_ylabel('Win Rate (%)')
            axes[0, 0].set_ylim(0, 100)
            for i, v in enumerate(results_df['win_rate']):
                axes[0, 0].text(i, v + 1, f'{v:.1f}%', ha='center', va='bottom')

            # 2. 總收益對比
            colors = ['green' if x > 0 else 'red' for x in results_df['total_pnl_pct']]
            axes[0, 1].bar(results_df['timeframe'], results_df['total_pnl_pct'], color=colors, alpha=0.7)
            axes[0, 1].set_title('Total Return by Timeframe')
            axes[0, 1].set_ylabel('Total Return (%)')
            axes[0, 1].axhline(y=0, color='black', linestyle='-', alpha=0.3)
            for i, v in enumerate(results_df['total_pnl_pct']):
                axes[0, 1].text(i, v + (1 if v > 0 else -1), f'{v:.1f}%', ha='center', va='bottom' if v > 0 else 'top')

            # 3. 盈利因子對比
            axes[1, 0].bar(results_df['timeframe'], results_df['profit_factor'], color='orange', alpha=0.7)
            axes[1, 0].set_title('Profit Factor by Timeframe')
            axes[1, 0].set_ylabel('Profit Factor')
            axes[1, 0].axhline(y=1, color='red', linestyle='--', alpha=0.5, label='Break-even')
            axes[1, 0].legend()
            for i, v in enumerate(results_df['profit_factor']):
                axes[1, 0].text(i, v + 0.05, f'{v:.2f}', ha='center', va='bottom')

            # 4. Sharpe比率對比
            axes[1, 1].bar(results_df['timeframe'], results_df['sharpe_ratio'], color='purple', alpha=0.7)
            axes[1, 1].set_title('Sharpe Ratio by Timeframe')
            axes[1, 1].set_ylabel('Sharpe Ratio')
            axes[1, 1].axhline(y=0, color='black', linestyle='-', alpha=0.3)
            for i, v in enumerate(results_df['sharpe_ratio']):
                axes[1, 1].text(i, v + 0.05, f'{v:.2f}', ha='center', va='bottom')

            plt.tight_layout()
            chart_file = f"{self.results_dir}/performance_charts.png"
            plt.savefig(chart_file, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"✅ 績效圖表已保存: {chart_file}")

        except Exception as e:
            print(f"❌ 圖表生成失敗: {e}")

def main():
    """主函數"""
    strategy = TIBreakoutStrategy()
    results = strategy.run_multi_timeframe_backtest()

    if results:
        print(f"\n✅ 成功完成 {len(results)} 個時框的回測")
        print("📁 生成的文件:")
        print("  - ti_breakout_results/comprehensive_results.csv")
        print("  - ti_breakout_results/strategy_report.txt")
        print("  - ti_breakout_results/performance_charts.png")
        print("  - ti_breakout_results/trades_*.csv (各時框交易記錄)")
    else:
        print("\n❌ 回測失敗，請檢查數據文件")

if __name__ == "__main__":
    main()
