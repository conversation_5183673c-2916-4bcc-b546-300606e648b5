"""
增強版 CVD + Taker Intensity 組合策略
結合 CVD 背離信號和 Blave Taker Intensity 數據提升策略表現

策略邏輯:
1. CVD 背離作為主要信號
2. Taker Intensity 作為確認信號
3. 多重過濾條件提升信號質量

作者: 專業量化策略工程師
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from cvd_divergence_indicator import CVDDivergenceIndicator
from cvd_trading_strategy import CVDTradingStrategy
import warnings
warnings.filterwarnings('ignore')

class EnhancedCVDTIStrategy(CVDTradingStrategy):
    """增強版 CVD + TI 策略"""
    
    def __init__(self, 
                 initial_capital: float = 10000,
                 risk_per_trade: float = 0.01,
                 leverage: float = 10.0,
                 take_profit_atr: float = 1.5,
                 stop_loss_atr: float = 1.0,
                 ti_confirmation: bool = True,
                 ti_threshold: float = 0.6):
        
        super().__init__(initial_capital, risk_per_trade, leverage, take_profit_atr, stop_loss_atr)
        self.ti_confirmation = ti_confirmation
        self.ti_threshold = ti_threshold
        
    def load_taker_intensity_data(self) -> pd.DataFrame:
        """載入 Taker Intensity 數據"""
        try:
            ti_df = pd.read_csv('blave_factor_data/taker_intensity_BTCUSDT_15min.csv', 
                              index_col=0, parse_dates=True)
            ti_df.columns = ['TI']
            print(f"✅ Taker Intensity 數據載入成功: {len(ti_df)} 條記錄")
            return ti_df
        except Exception as e:
            print(f"❌ Taker Intensity 數據載入失敗: {e}")
            return pd.DataFrame()
    
    def calculate_ti_indicators(self, ti_df: pd.DataFrame) -> pd.DataFrame:
        """計算 TI 相關指標"""
        ti_enhanced = ti_df.copy()
        
        # 1. TI 移動平均
        ti_enhanced['TI_MA_20'] = ti_enhanced['TI'].rolling(20).mean()
        ti_enhanced['TI_MA_50'] = ti_enhanced['TI'].rolling(50).mean()
        
        # 2. TI 標準化 (Z-score)
        ti_enhanced['TI_zscore'] = (ti_enhanced['TI'] - ti_enhanced['TI'].rolling(100).mean()) / ti_enhanced['TI'].rolling(100).std()
        
        # 3. TI 百分位數 (修復 pandas 兼容性)
        def rolling_percentile(series, window):
            result = []
            for i in range(len(series)):
                if i < window - 1:
                    result.append(np.nan)
                else:
                    window_data = series.iloc[i-window+1:i+1]
                    percentile = (window_data < series.iloc[i]).sum() / len(window_data)
                    result.append(percentile)
            return pd.Series(result, index=series.index)

        ti_enhanced['TI_percentile'] = rolling_percentile(ti_enhanced['TI'], 200)
        
        # 4. TI 動量
        ti_enhanced['TI_momentum'] = ti_enhanced['TI'] - ti_enhanced['TI'].shift(10)
        
        # 5. TI 絕對值 (基於之前分析，abs(TI) 表現最佳)
        ti_enhanced['TI_abs'] = np.abs(ti_enhanced['TI'])
        
        # 6. TI 趨勢方向
        ti_enhanced['TI_trend'] = np.where(ti_enhanced['TI'] > ti_enhanced['TI_MA_20'], 1, -1)
        
        return ti_enhanced
    
    def generate_ti_confirmation_signals(self, ti_df: pd.DataFrame, signal_type: str, signal_time: pd.Timestamp) -> dict:
        """生成 TI 確認信號"""
        if signal_time not in ti_df.index:
            return {'confirmed': False, 'reason': 'No TI data'}
        
        ti_row = ti_df.loc[signal_time]
        confirmation = {'confirmed': False, 'reason': '', 'ti_score': 0}
        
        # 獲取 TI 指標值
        ti_value = ti_row['TI']
        ti_percentile = ti_row['TI_percentile']
        ti_zscore = ti_row['TI_zscore']
        ti_momentum = ti_row['TI_momentum']
        ti_trend = ti_row['TI_trend']
        
        score = 0
        reasons = []
        
        if signal_type == 'LONG':
            # 多頭信號確認條件
            
            # 1. TI 百分位數確認 (基於之前分析，TI 在高百分位時有正相關)
            if ti_percentile > 0.7:
                score += 2
                reasons.append('TI高百分位')
            elif ti_percentile > 0.5:
                score += 1
                reasons.append('TI中高百分位')
            
            # 2. TI 動量確認
            if ti_momentum > 0:
                score += 1
                reasons.append('TI動量向上')
            
            # 3. TI 趨勢確認
            if ti_trend > 0:
                score += 1
                reasons.append('TI趨勢向上')
            
            # 4. TI 絕對值確認 (基於之前分析，abs(TI) 與收益率正相關)
            if np.abs(ti_value) > np.abs(ti_df['TI']).rolling(100).mean().loc[signal_time]:
                score += 1
                reasons.append('TI絕對值較高')
        
        else:  # SHORT
            # 空頭信號確認條件
            
            # 1. TI 百分位數確認 (低百分位時可能有負相關)
            if ti_percentile < 0.3:
                score += 2
                reasons.append('TI低百分位')
            elif ti_percentile < 0.5:
                score += 1
                reasons.append('TI中低百分位')
            
            # 2. TI 動量確認
            if ti_momentum < 0:
                score += 1
                reasons.append('TI動量向下')
            
            # 3. TI 趨勢確認
            if ti_trend < 0:
                score += 1
                reasons.append('TI趨勢向下')
            
            # 4. TI 絕對值確認
            if np.abs(ti_value) > np.abs(ti_df['TI']).rolling(100).mean().loc[signal_time]:
                score += 1
                reasons.append('TI絕對值較高')
        
        # 確認邏輯
        max_score = 5
        confirmation_threshold = max_score * self.ti_threshold
        
        if score >= confirmation_threshold:
            confirmation['confirmed'] = True
            confirmation['reason'] = f"TI確認 ({score}/{max_score}): " + ", ".join(reasons)
        else:
            confirmation['confirmed'] = False
            confirmation['reason'] = f"TI未確認 ({score}/{max_score}): " + ", ".join(reasons)
        
        confirmation['ti_score'] = score / max_score
        confirmation['ti_value'] = ti_value
        confirmation['ti_percentile'] = ti_percentile
        
        return confirmation
    
    def run_enhanced_backtest(self, df: pd.DataFrame, start_date: str = None, end_date: str = None) -> dict:
        """運行增強版回測"""
        print("🚀 開始 CVD + TI 增強策略回測...")
        
        # 載入 TI 數據
        ti_df = self.load_taker_intensity_data()
        if ti_df.empty:
            print("❌ 無法載入 TI 數據，回退到純 CVD 策略")
            return self.run_backtest(df, start_date, end_date)
        
        # 計算 TI 指標
        ti_enhanced = self.calculate_ti_indicators(ti_df)
        
        # 篩選回測期間
        if start_date and end_date:
            mask = (df.index >= start_date) & (df.index <= end_date)
            backtest_df = df[mask].copy()
            ti_backtest = ti_enhanced[mask].copy()
        else:
            backtest_df = df.tail(int(365 * 24 * 4 * 2)).copy()
            ti_backtest = ti_enhanced.tail(int(365 * 24 * 4 * 2)).copy()
        
        print(f"📅 回測期間: {backtest_df.index[0]} 到 {backtest_df.index[-1]}")
        print(f"📊 數據量: {len(backtest_df)} 條記錄")
        
        # 生成 CVD 信號
        cvd_indicator = CVDDivergenceIndicator()
        signals_df = cvd_indicator.generate_signals_with_atr(backtest_df)
        
        # 初始化
        account_balance = self.initial_capital
        open_trades = []
        closed_trades = []
        equity_history = []
        
        # 獲取所有 CVD 信號
        cvd_signal_indices = signals_df[signals_df['Bull_Signal'] | signals_df['Bear_Signal']].index
        
        # 應用 TI 過濾
        confirmed_signals = []
        rejected_signals = []
        
        for signal_time in cvd_signal_indices:
            signal_row = signals_df.loc[signal_time]
            
            if signal_row['Bull_Signal']:
                signal_type = 'LONG'
            else:
                signal_type = 'SHORT'
            
            # TI 確認
            if self.ti_confirmation:
                ti_confirmation = self.generate_ti_confirmation_signals(ti_backtest, signal_type, signal_time)
                
                if ti_confirmation['confirmed']:
                    confirmed_signals.append({
                        'time': signal_time,
                        'type': signal_type,
                        'ti_score': ti_confirmation['ti_score'],
                        'ti_reason': ti_confirmation['reason']
                    })
                else:
                    rejected_signals.append({
                        'time': signal_time,
                        'type': signal_type,
                        'ti_score': ti_confirmation['ti_score'],
                        'ti_reason': ti_confirmation['reason']
                    })
            else:
                confirmed_signals.append({
                    'time': signal_time,
                    'type': signal_type,
                    'ti_score': 1.0,
                    'ti_reason': 'No TI filter'
                })
        
        confirmed_signal_times = [s['time'] for s in confirmed_signals]
        
        print(f"🎯 CVD 原始信號: {len(cvd_signal_indices)}")
        print(f"✅ TI 確認信號: {len(confirmed_signals)}")
        print(f"❌ TI 拒絕信號: {len(rejected_signals)}")
        print(f"📈 信號過濾率: {len(rejected_signals)/len(cvd_signal_indices)*100:.1f}%")
        
        # 執行回測 (使用確認的信號)
        for i, (current_time, current_bar) in enumerate(backtest_df.iterrows()):
            
            # 檢查現有交易的平倉條件
            for trade in open_trades[:]:
                updated_trade = self.check_trade_exit(trade, current_bar)
                if updated_trade['status'] == 'CLOSED':
                    closed_trades.append(updated_trade)
                    open_trades.remove(trade)
                    account_balance += updated_trade['pnl']
            
            # 檢查是否有確認的新信號
            if current_time in confirmed_signal_times:
                signal_row = signals_df.loc[current_time]
                
                # 確保有下一根K線用於進場
                next_index = i + 1
                if next_index < len(backtest_df):
                    next_bar = backtest_df.iloc[next_index]
                    
                    # 執行交易
                    new_trade = self.execute_trade(signal_row, next_bar, account_balance)
                    if new_trade:
                        # 添加 TI 信息
                        signal_info = next(s for s in confirmed_signals if s['time'] == current_time)
                        new_trade['ti_score'] = signal_info['ti_score']
                        new_trade['ti_reason'] = signal_info['ti_reason']
                        open_trades.append(new_trade)
            
            # 記錄權益曲線
            total_unrealized_pnl = 0
            for trade in open_trades:
                price_change_pct = (current_bar['Close'] - trade['entry_price']) / trade['entry_price']
                if trade['signal_type'] == 'SHORT':
                    price_change_pct = -price_change_pct
                
                leveraged_return = price_change_pct * self.leverage
                invested_amount = trade['account_balance'] * self.risk_per_trade
                unrealized_pnl = invested_amount * leveraged_return
                
                # 限制未實現盈虧
                max_pnl = trade['account_balance'] * 0.5
                unrealized_pnl = max(-max_pnl, min(unrealized_pnl, max_pnl))
                total_unrealized_pnl += unrealized_pnl
            
            current_equity = account_balance + total_unrealized_pnl
            equity_history.append({
                'time': current_time,
                'equity': current_equity,
                'balance': account_balance,
                'unrealized_pnl': total_unrealized_pnl,
                'open_trades': len(open_trades)
            })
        
        # 強制平倉所有未平倉交易
        final_bar = backtest_df.iloc[-1]
        for trade in open_trades:
            price_change_pct = (final_bar['Close'] - trade['entry_price']) / trade['entry_price']
            if trade['signal_type'] == 'SHORT':
                price_change_pct = -price_change_pct
            
            leveraged_return = price_change_pct * self.leverage
            invested_amount = trade['account_balance'] * self.risk_per_trade
            pnl = invested_amount * leveraged_return
            
            max_pnl = trade['account_balance'] * 0.5
            pnl = max(-max_pnl, min(pnl, max_pnl))
            
            trade.update({
                'exit_time': final_bar.name,
                'exit_price': final_bar['Close'],
                'exit_reason': 'FORCE_CLOSE',
                'pnl': pnl,
                'pnl_percentage': pnl / trade['account_balance'],
                'leveraged_return': leveraged_return,
                'status': 'CLOSED'
            })
            closed_trades.append(trade)
            account_balance += pnl
        
        # 保存結果
        self.trades = closed_trades
        self.equity_curve = equity_history
        
        # 保存信號分析
        self.signal_analysis = {
            'original_signals': len(cvd_signal_indices),
            'confirmed_signals': confirmed_signals,
            'rejected_signals': rejected_signals,
            'filter_rate': len(rejected_signals)/len(cvd_signal_indices) if len(cvd_signal_indices) > 0 else 0
        }
        
        # 計算績效指標
        performance = self.calculate_performance_metrics()
        
        print(f"\n📈 增強策略回測完成!")
        print(f"  總交易次數: {len(closed_trades)}")
        print(f"  最終資金: ${account_balance:,.2f}")
        print(f"  總收益率: {((account_balance / self.initial_capital) - 1) * 100:.2f}%")
        
        return performance
    
    def compare_strategies(self, df: pd.DataFrame) -> dict:
        """比較原始 CVD 策略和增強策略"""
        print("\n🔄 策略對比分析...")
        
        # 運行原始 CVD 策略
        print("\n1️⃣ 運行原始 CVD 策略:")
        original_strategy = CVDTradingStrategy(
            initial_capital=self.initial_capital,
            risk_per_trade=self.risk_per_trade,
            leverage=self.leverage,
            take_profit_atr=self.take_profit_atr,
            stop_loss_atr=self.stop_loss_atr
        )
        original_performance = original_strategy.run_backtest(df)
        
        # 運行增強策略
        print("\n2️⃣ 運行 CVD + TI 增強策略:")
        enhanced_performance = self.run_enhanced_backtest(df)
        
        # 對比結果
        comparison = {
            'original': original_performance,
            'enhanced': enhanced_performance,
            'improvement': {}
        }
        
        if original_performance and enhanced_performance:
            comparison['improvement'] = {
                'return_improvement': enhanced_performance['total_return'] - original_performance['total_return'],
                'winrate_improvement': enhanced_performance['win_rate'] - original_performance['win_rate'],
                'sharpe_improvement': enhanced_performance['sharpe_ratio'] - original_performance['sharpe_ratio'],
                'drawdown_improvement': original_performance['max_drawdown'] - enhanced_performance['max_drawdown'],
                'trades_reduction': original_performance['total_trades'] - enhanced_performance['total_trades']
            }
        
        return comparison

def main():
    """主函數"""
    print("🚀 CVD + Taker Intensity 增強策略回測系統")
    print("=" * 70)
    
    # 載入數據
    try:
        df = pd.read_csv('binance_data/BTCUSDT_15m_enhanced.csv', index_col=0, parse_dates=True)
        print(f"✅ 價格數據載入成功: {len(df)} 條記錄")
    except Exception as e:
        print(f"❌ 數據載入失敗: {e}")
        return
    
    # 創建增強策略
    enhanced_strategy = EnhancedCVDTIStrategy(
        initial_capital=10000,
        risk_per_trade=0.01,
        leverage=10.0,
        take_profit_atr=1.5,
        stop_loss_atr=1.0,
        ti_confirmation=True,
        ti_threshold=0.6
    )
    
    # 策略對比
    comparison = enhanced_strategy.compare_strategies(df)
    
    # 顯示對比結果
    if comparison['original'] and comparison['enhanced']:
        print(f"\n📊 策略對比結果:")
        print(f"{'指標':<15} {'原始CVD':<12} {'CVD+TI':<12} {'改善':<12}")
        print("-" * 55)
        print(f"{'總收益率':<15} {comparison['original']['total_return']:<11.2f}% {comparison['enhanced']['total_return']:<11.2f}% {comparison['improvement']['return_improvement']:+.2f}%")
        print(f"{'勝率':<15} {comparison['original']['win_rate']:<11.2%} {comparison['enhanced']['win_rate']:<11.2%} {comparison['improvement']['winrate_improvement']:+.2%}")
        print(f"{'Sharpe比率':<15} {comparison['original']['sharpe_ratio']:<11.2f} {comparison['enhanced']['sharpe_ratio']:<11.2f} {comparison['improvement']['sharpe_improvement']:+.2f}")
        print(f"{'最大回撤':<15} {comparison['original']['max_drawdown']:<11.2f}% {comparison['enhanced']['max_drawdown']:<11.2f}% {comparison['improvement']['drawdown_improvement']:+.2f}%")
        print(f"{'交易次數':<15} {comparison['original']['total_trades']:<11} {comparison['enhanced']['total_trades']:<11} {comparison['improvement']['trades_reduction']:+}")
    
    # 繪製和保存結果
    enhanced_strategy.plot_results()
    enhanced_strategy.save_results()
    
    print("\n🎉 增強策略回測完成!")

if __name__ == "__main__":
    main()
