"""
幣安 BTC 永續合約數據下載器
下載 BTCUSDT 永續合約的 15分鐘 K線數據

作者: 專業量化策略工程師
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import json
from typing import Optional, List
import logging

class BinanceDataDownloader:
    """幣安數據下載器"""
    
    def __init__(self):
        # 幣安 API 配置
        self.base_url = "https://fapi.binance.com"  # 期貨API
        
        # 設置日誌
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 創建數據存儲目錄
        self.data_dir = "binance_data"
        os.makedirs(self.data_dir, exist_ok=True)
        
        self.session = None
    
    async def __aenter__(self):
        """異步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """異步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    def date_to_timestamp(self, date_str: str) -> int:
        """將日期字符串轉換為毫秒時間戳"""
        dt = pd.to_datetime(date_str)
        return int(dt.timestamp() * 1000)
    
    async def get_kline_data(self, symbol: str = "BTCUSDT", interval: str = "15m",
                            start_date: str = "2023-12-31", end_date: str = "2025-06-30") -> Optional[pd.DataFrame]:
        """獲取 K線數據"""
        try:
            self.logger.info(f"🚀 開始下載 {symbol} {interval} K線數據")
            self.logger.info(f"📅 時間範圍: {start_date} 到 {end_date}")
            
            start_timestamp = self.date_to_timestamp(start_date)
            end_timestamp = self.date_to_timestamp(end_date)
            
            all_data = []
            current_start = start_timestamp
            
            # 幣安API每次最多返回1500條數據，需要分批獲取
            while current_start < end_timestamp:
                url = f"{self.base_url}/fapi/v1/klines"
                params = {
                    "symbol": symbol,
                    "interval": interval,
                    "startTime": current_start,
                    "endTime": end_timestamp,
                    "limit": 1500
                }
                
                self.logger.info(f"📥 獲取數據: {datetime.fromtimestamp(current_start/1000)} 開始")
                
                async with self.session.get(url, params=params, timeout=30) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if data:
                            all_data.extend(data)
                            
                            # 更新下一批的開始時間
                            last_timestamp = int(data[-1][0])
                            current_start = last_timestamp + 1
                            
                            self.logger.info(f"✅ 獲取 {len(data)} 條記錄")
                        else:
                            break
                    else:
                        self.logger.error(f"❌ API響應異常: {response.status}")
                        break
                
                # 避免請求過於頻繁
                await asyncio.sleep(0.1)
            
            if all_data:
                # 轉換為 DataFrame
                df = pd.DataFrame(all_data, columns=[
                    'timestamp', 'open', 'high', 'low', 'close', 'volume',
                    'close_time', 'quote_asset_volume', 'number_of_trades',
                    'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
                ])
                
                # 轉換數據類型
                numeric_columns = ['open', 'high', 'low', 'close', 'volume', 
                                 'quote_asset_volume', 'number_of_trades',
                                 'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume']
                
                for col in numeric_columns:
                    df[col] = pd.to_numeric(df[col])
                
                # 轉換時間戳
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                df['close_time'] = pd.to_datetime(df['close_time'], unit='ms')
                
                # 設置索引
                df.set_index('timestamp', inplace=True)
                df = df.sort_index()
                
                # 去重
                df = df[~df.index.duplicated(keep='first')]
                
                # 重命名主要列
                df = df.rename(columns={
                    'open': 'Open',
                    'high': 'High',
                    'low': 'Low',
                    'close': 'Close',
                    'volume': 'Volume'
                })
                
                self.logger.info(f"✅ {symbol} K線數據獲取成功: {len(df)}條記錄")
                self.logger.info(f"📊 數據範圍: {df.index.min()} 到 {df.index.max()}")
                
                return df
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ K線數據獲取失敗: {e}")
            return None
    
    async def download_btc_data(self, start_date: str = "2023-12-31", 
                               end_date: str = "2025-06-30") -> Optional[pd.DataFrame]:
        """下載 BTC 永續合約數據"""
        symbol = "BTCUSDT"
        interval = "15m"
        
        # 獲取數據
        kline_data = await self.get_kline_data(
            symbol=symbol,
            interval=interval,
            start_date=start_date,
            end_date=end_date
        )
        
        if kline_data is not None:
            # 保存到文件
            filename = f"{self.data_dir}/{symbol}_{interval}_klines.csv"
            kline_data.to_csv(filename)
            self.logger.info(f"💾 數據已保存到: {filename}")
            
            # 保存摘要信息
            summary = {
                "symbol": symbol,
                "interval": interval,
                "download_time": datetime.now().isoformat(),
                "records_count": len(kline_data),
                "date_range": {
                    "start": kline_data.index.min().isoformat(),
                    "end": kline_data.index.max().isoformat()
                },
                "columns": list(kline_data.columns),
                "price_stats": {
                    "min_price": float(kline_data['Low'].min()),
                    "max_price": float(kline_data['High'].max()),
                    "avg_price": float(kline_data['Close'].mean()),
                    "total_volume": float(kline_data['Volume'].sum())
                }
            }
            
            summary_file = f"{self.data_dir}/{symbol}_{interval}_summary.json"
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"📋 數據摘要已保存到: {summary_file}")
            
            return kline_data
        
        return None
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """計算技術指標"""
        try:
            # 計算收益率
            df['returns'] = df['Close'].pct_change()
            
            # 計算移動平均線
            df['MA_20'] = df['Close'].rolling(window=20).mean()
            df['MA_50'] = df['Close'].rolling(window=50).mean()
            
            # 計算波動率
            df['volatility'] = df['returns'].rolling(window=20).std()
            
            # 計算價格變化
            df['price_change'] = df['Close'].diff()
            df['price_change_pct'] = df['Close'].pct_change() * 100
            
            # 計算成交量變化
            df['volume_change'] = df['Volume'].diff()
            df['volume_change_pct'] = df['Volume'].pct_change() * 100
            
            self.logger.info("✅ 技術指標計算完成")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 技術指標計算失敗: {e}")
            return df

async def main():
    """主函數 - 下載幣安 BTC 數據"""
    async with BinanceDataDownloader() as downloader:
        # 下載 BTCUSDT 15分鐘 K線數據
        btc_data = await downloader.download_btc_data(
            start_date="2023-12-31",
            end_date="2025-06-30"
        )
        
        if btc_data is not None:
            # 計算技術指標
            btc_data_with_indicators = downloader.calculate_technical_indicators(btc_data)
            
            # 重新保存包含指標的數據
            enhanced_filename = f"{downloader.data_dir}/BTCUSDT_15m_enhanced.csv"
            btc_data_with_indicators.to_csv(enhanced_filename)
            downloader.logger.info(f"💾 增強數據已保存到: {enhanced_filename}")
            
            print("\n" + "="*60)
            print("📊 幣安 BTC 數據下載完成!")
            print("="*60)
            print(f"數據記錄數量: {len(btc_data_with_indicators)}")
            print(f"時間範圍: {btc_data_with_indicators.index.min()} 到 {btc_data_with_indicators.index.max()}")
            print(f"價格範圍: ${btc_data_with_indicators['Low'].min():.2f} - ${btc_data_with_indicators['High'].max():.2f}")
            print(f"平均價格: ${btc_data_with_indicators['Close'].mean():.2f}")
            print("="*60)
        else:
            print("❌ 數據下載失敗")

if __name__ == "__main__":
    asyncio.run(main())
